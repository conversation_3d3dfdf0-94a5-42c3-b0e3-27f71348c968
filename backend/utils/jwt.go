package utils

import (
	"os"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// getJWTSecret 从环境变量获取JWT密钥
func getJWTSecret() []byte {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = "your-super-secret-jwt-key-change-this-in-production" // 与.env文件保持一致
	}
	return []byte(secret)
}

// GenerateJWT 生成JWT令牌
func GenerateJWT(userID uint, username string) (string, error) {
	// 使用MapClaims确保与中间件解析一致
	claims := jwt.MapClaims{
		"user_id":  userID,
		"username": username,
		"exp":      time.Now().Add(24 * time.Hour).Unix(),
		"iat":      time.Now().Unix(),
		"nbf":      time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(getJWTSecret())
}

// ParseJWT 解析JWT令牌
func ParseJWT(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return getJWTSecret(), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrInvalidKey
}

// ValidateJWT 验证JWT令牌
func ValidateJWT(tokenString string) bool {
	_, err := ParseJWT(tokenString)
	return err == nil
}

// GetJWTSecret 获取JWT密钥（供其他包使用）
func GetJWTSecret() string {
	return string(getJWTSecret())
}
