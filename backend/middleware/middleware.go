package middleware

import (
	"net/http"
	"strings"
	"time"

	"file-manager/models"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logrus.WithFields(logrus.Fields{
			"status":     param.StatusCode,
			"method":     param.Method,
			"path":       param.Path,
			"ip":         param.ClientIP,
			"user_agent": param.Request.UserAgent(),
			"latency":    param.Latency,
		}).Info("HTTP Request")
		return ""
	})
}

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		if len(c.<PERSON>rro<PERSON>) > 0 {
			err := c.Errors.Last()
			logrus.WithFields(logrus.Fields{
				"error":  err.Error(),
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
			}).Error("Request Error")

			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Internal server error",
				"error":   err.Error(),
			})
		}
	}
}

// AuthMiddleware JWT认证中间件
func AuthMiddleware(jwtSecret string, db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logrus.WithFields(logrus.Fields{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
				"ip":     c.ClientIP(),
			}).Warn("Missing authorization header")

			c.JSON(http.StatusUnauthorized, gin.H{
				"success":    false,
				"message":    "Authorization header required",
				"error_type": "missing_header",
			})
			c.Abort()
			return
		}

		// 检查Bearer格式
		if !strings.HasPrefix(authHeader, "Bearer ") {
			logrus.WithFields(logrus.Fields{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
				"ip":     c.ClientIP(),
			}).Warn("Invalid authorization header format")

			c.JSON(http.StatusUnauthorized, gin.H{
				"success":    false,
				"message":    "Invalid authorization header format",
				"error_type": "invalid_format",
			})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success":    false,
				"message":    "Empty token",
				"error_type": "empty_token",
			})
			c.Abort()
			return
		}

		// 解析token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// 验证签名方法
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, jwt.ErrSignatureInvalid
			}
			return []byte(jwtSecret), nil
		})

		if err != nil {
			var errorType, message string

			// 详细的错误分类
			switch {
			case err == jwt.ErrTokenExpired:
				errorType = "token_expired"
				message = "Token has expired"
			case err == jwt.ErrTokenNotValidYet:
				errorType = "token_not_valid_yet"
				message = "Token is not valid yet"
			case err == jwt.ErrSignatureInvalid:
				errorType = "invalid_signature"
				message = "Invalid token signature"
			case err == jwt.ErrTokenMalformed:
				errorType = "malformed_token"
				message = "Malformed token"
			default:
				errorType = "invalid_token"
				message = "Invalid token"
			}

			logrus.WithFields(logrus.Fields{
				"error":      err.Error(),
				"error_type": errorType,
				"path":       c.Request.URL.Path,
				"method":     c.Request.Method,
				"ip":         c.ClientIP(),
			}).Warn("JWT token validation failed")

			c.JSON(http.StatusUnauthorized, gin.H{
				"success":    false,
				"message":    message,
				"error_type": errorType,
			})
			c.Abort()
			return
		}

		if !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success":    false,
				"message":    "Invalid token",
				"error_type": "invalid_token",
			})
			c.Abort()
			return
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success":    false,
				"message":    "Invalid token claims",
				"error_type": "invalid_claims",
			})
			c.Abort()
			return
		}

		// 检查token过期时间
		if exp, ok := claims["exp"].(float64); ok {
			if time.Now().Unix() > int64(exp) {
				c.JSON(http.StatusUnauthorized, gin.H{
					"success":    false,
					"message":    "Token has expired",
					"error_type": "token_expired",
				})
				c.Abort()
				return
			}
		}

		// 提取用户ID
		var userID uint
		if userIDFloat, ok := claims["user_id"].(float64); ok {
			userID = uint(userIDFloat)
		} else if userIDInt, ok := claims["user_id"].(int); ok {
			userID = uint(userIDInt)
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success":    false,
				"message":    "Invalid user ID in token",
				"error_type": "invalid_user_id",
			})
			c.Abort()
			return
		}

		// 验证用户是否存在
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				logrus.WithFields(logrus.Fields{
					"user_id": userID,
					"path":    c.Request.URL.Path,
					"method":  c.Request.Method,
					"ip":      c.ClientIP(),
				}).Warn("User not found for valid token")

				c.JSON(http.StatusUnauthorized, gin.H{
					"success":    false,
					"message":    "User not found",
					"error_type": "user_not_found",
				})
				c.Abort()
				return
			}

			logrus.WithFields(logrus.Fields{
				"error":   err.Error(),
				"user_id": userID,
				"path":    c.Request.URL.Path,
				"method":  c.Request.Method,
			}).Error("Database error during user validation")

			c.JSON(http.StatusInternalServerError, gin.H{
				"success":    false,
				"message":    "Internal server error",
				"error_type": "database_error",
			})
			c.Abort()
			return
		}

		// 设置用户信息到上下文
		c.Set("user_id", userID)
		c.Set("username", claims["username"])
		c.Set("user_role", user.Role)
		c.Set("current_user", user)

		c.Next()
	}
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimit 限流中间件（简单实现）
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以实现更复杂的限流逻辑
		// 例如使用Redis或内存存储来跟踪请求频率
		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

func generateRequestID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
