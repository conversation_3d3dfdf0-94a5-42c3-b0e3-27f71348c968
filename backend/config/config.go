package config

import (
	"log"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

type Config struct {
	Environment  string `mapstructure:"ENVIRONMENT"`
	DatabaseURL  string `mapstructure:"DATABASE_URL"`
	JWTSecret    string `mapstructure:"JWT_SECRET"`
	Port         string `mapstructure:"PORT"`
	UploadPath   string `mapstructure:"UPLOAD_PATH"`
	MaxFileSize  int64  `mapstructure:"MAX_FILE_SIZE"`
	AllowedTypes string `mapstructure:"ALLOWED_TYPES"`
	LogLevel     string `mapstructure:"LOG_LEVEL"`
}

func Load() *Config {
	viper.SetConfigName(".env")
	viper.SetConfigType("env")
	viper.AddConfigPath(".")
	viper.AutomaticEnv()

	// 获取当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatal("Failed to get working directory:", err)
	}

	// 设置默认值 - 使用绝对路径
	viper.SetDefault("ENVIRONMENT", "development")
	viper.SetDefault("DATABASE_URL", filepath.Join(workDir, "file_manager.db"))
	viper.SetDefault("JWT_SECRET", "your-secret-key")
	viper.SetDefault("PORT", "8080")
	viper.SetDefault("UPLOAD_PATH", filepath.Join(workDir, "uploads"))
	viper.SetDefault("MAX_FILE_SIZE", 100*1024*1024) // 100MB
	viper.SetDefault("ALLOWED_TYPES", "jpg,jpeg,png,gif,pdf,txt,doc,docx,xls,xlsx,zip,rar")
	viper.SetDefault("LOG_LEVEL", "info")

	if err := viper.ReadInConfig(); err != nil {
		log.Println("No config file found, using defaults and environment variables")
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		log.Fatal("Unable to decode config:", err)
	}

	// 如果配置中的路径是相对路径，转换为绝对路径
	if !filepath.IsAbs(config.DatabaseURL) {
		config.DatabaseURL = filepath.Join(workDir, config.DatabaseURL)
	}
	if !filepath.IsAbs(config.UploadPath) {
		config.UploadPath = filepath.Join(workDir, config.UploadPath)
	}

	// 确保上传目录存在
	if err := os.MkdirAll(config.UploadPath, 0755); err != nil {
		log.Fatal("Failed to create upload directory:", err)
	}

	log.Printf("配置加载完成:")
	log.Printf("  数据库路径: %s", config.DatabaseURL)
	log.Printf("  上传路径: %s", config.UploadPath)

	return &config
}
