package models

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Username  string         `json:"username" gorm:"uniqueIndex;not null"`
	Email     string         `json:"email" gorm:"uniqueIndex;not null"`
	Password  string         `json:"-" gorm:"not null"`
	Role      string         `json:"role" gorm:"default:user"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Directory 目录模型
type Directory struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Name      string         `json:"name" gorm:"not null"`
	Path      string         `json:"path" gorm:"uniqueIndex;not null"`
	ParentID  *uint          `json:"parent_id"`
	Parent    *Directory     `json:"parent" gorm:"foreignKey:ParentID"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	User      User           `json:"user" gorm:"foreignKey:UserID"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// File 文件模型
type File struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Name         string         `json:"name" gorm:"not null"`
	OriginalName string         `json:"original_name" gorm:"not null"`
	Path         string         `json:"path" gorm:"not null"`
	Size         int64          `json:"size" gorm:"not null"`
	Hash         string         `json:"hash" gorm:"index;default:''"` // SHA256文件内容hash
	MimeType     string         `json:"mime_type" gorm:"not null"`
	Extension    string         `json:"extension" gorm:"not null"`
	DirectoryID  *uint          `json:"directory_id"`
	Directory    *Directory     `json:"directory" gorm:"foreignKey:DirectoryID"`
	UserID       uint           `json:"user_id" gorm:"not null"`
	User         User           `json:"user" gorm:"foreignKey:UserID"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// RenameOperation 重命名操作模型
type RenameOperation struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Type        string         `json:"type" gorm:"not null"` // regex, sequence, prefix, suffix, case
	Pattern     string         `json:"pattern"`              // 正则表达式模式
	Replacement string         `json:"replacement"`          // 替换内容
	Prefix      string         `json:"prefix"`               // 前缀
	Suffix      string         `json:"suffix"`               // 后缀
	CaseType    string         `json:"case_type"`            // upper, lower, title
	StartNumber int            `json:"start_number"`         // 序号起始值
	UserID      uint           `json:"user_id" gorm:"not null"`
	User        User           `json:"user" gorm:"foreignKey:UserID"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// OperationLog 操作日志模型
type OperationLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Action    string         `json:"action" gorm:"not null"`   // upload, download, rename, delete, etc.
	Resource  string         `json:"resource" gorm:"not null"` // 操作的资源
	OldValue  string         `json:"old_value"`                // 操作前的值
	NewValue  string         `json:"new_value"`                // 操作后的值
	Status    string         `json:"status" gorm:"not null"`   // success, failed
	ErrorMsg  string         `json:"error_msg"`                // 错误信息
	UserID    uint           `json:"user_id" gorm:"not null"`
	User      User           `json:"user" gorm:"foreignKey:UserID"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// RenamePreview 重命名预览结构
type RenamePreview struct {
	OriginalName string `json:"original_name"`
	NewName      string `json:"new_name"`
	Valid        bool   `json:"valid"`
	Error        string `json:"error,omitempty"`
}

// FileInfo 文件信息结构
type FileInfo struct {
	Name      string    `json:"name"`
	Size      int64     `json:"size"`
	ModTime   time.Time `json:"mod_time"`
	IsDir     bool      `json:"is_dir"`
	Extension string    `json:"extension"`
	MimeType  string    `json:"mime_type"`
}

// UploadResponse 上传响应结构
type UploadResponse struct {
	Success bool     `json:"success"`
	Message string   `json:"message"`
	Files   []File   `json:"files,omitempty"`
	Errors  []string `json:"errors,omitempty"`
}
