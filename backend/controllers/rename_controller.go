package controllers

import (
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"file-manager/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RenameController struct {
	db *gorm.DB
}

func NewRenameController(db *gorm.DB) *RenameController {
	return &RenameController{db: db}
}

type RenameRequest struct {
	FileIDs       []uint `json:"file_ids" binding:"required"`
	Type          string `json:"type" binding:"required"` // regex, sequence, prefix, suffix, case, extension, replace, mapping
	Pattern       string `json:"pattern"`                 // 正则表达式模式
	Replacement   string `json:"replacement"`             // 替换内容
	Prefix        string `json:"prefix"`                  // 前缀
	Suffix        string `json:"suffix"`                  // 后缀
	CaseType      string `json:"case_type"`               // upper, lower, title
	StartNumber   int    `json:"start_number"`            // 序号起始值
	NewExtension  string `json:"new_extension"`           // 新扩展名
	FindText      string `json:"find_text"`               // 查找文本（用于replace类型）
	ReplaceText   string `json:"replace_text"`            // 替换文本（用于replace类型）
	BaseName      string `json:"base_name"`               // 基础名称（用于sequence类型）
	NumberPadding int    `json:"number_padding"`          // 数字位数（用于sequence类型）
	MappingFrom   string `json:"mapping_from"`            // 映射源列表（用于mapping类型）
	MappingTo     string `json:"mapping_to"`              // 映射目标列表（用于mapping类型）
}

// PreviewRename 预览重命名结果
func (rc *RenameController) PreviewRename(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req RenameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 获取要重命名的文件
	var files []models.File
	if err := rc.db.Where("id IN ? AND user_id = ?", req.FileIDs, userID).Find(&files).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch files",
			"error":   err.Error(),
		})
		return
	}

	if len(files) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "No files found",
		})
		return
	}

	// 生成预览结果
	previews := make([]models.RenamePreview, len(files))
	for i, file := range files {
		newName, err := rc.generateNewName(file.OriginalName, req, i)
		previews[i] = models.RenamePreview{
			OriginalName: file.OriginalName,
			NewName:      newName,
			Valid:        err == nil,
		}
		if err != nil {
			previews[i].Error = err.Error()
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    previews,
	})
}

// DownloadRenamedFiles 下载重命名后的文件（不修改数据库）
func (rc *RenameController) DownloadRenamedFiles(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req RenameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 添加调试日志
	fmt.Printf("DEBUG: Received rename request - Type: %s, FindText: '%s', ReplaceText: '%s'\n",
		req.Type, req.FindText, req.ReplaceText)
	fmt.Printf("DEBUG: MappingFrom: '%s', MappingTo: '%s'\n", req.MappingFrom, req.MappingTo)

	// 获取要重命名的文件
	var files []models.File
	if err := rc.db.Where("id IN ? AND user_id = ?", req.FileIDs, userID).Find(&files).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch files",
			"error":   err.Error(),
		})
		return
	}

	if len(files) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "No files found",
		})
		return
	}

	// 生成重命名预览结果（不修改数据库）
	var previews []models.RenamePreview
	for i, file := range files {
		newName, err := rc.generateNewName(file.OriginalName, req, i)
		preview := models.RenamePreview{
			OriginalName: file.OriginalName,
			NewName:      newName,
			Valid:        err == nil,
		}
		if err != nil {
			preview.Error = err.Error()
		}
		previews = append(previews, preview)
	}

	// 返回重命名预览结果，让前端处理下载
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Rename preview generated successfully",
		"data": gin.H{
			"previews":    previews,
			"file_ids":    req.FileIDs,
			"total_count": len(files),
		},
	})
}

// generateNewName 根据规则生成新文件名
func (rc *RenameController) generateNewName(originalName string, req RenameRequest, index int) (string, error) {
	switch req.Type {
	case "regex":
		return rc.applyRegexRename(originalName, req.Pattern, req.Replacement)
	case "sequence":
		return rc.applySequenceRename(originalName, req.StartNumber+index)
	case "prefix":
		return rc.applyPrefixRename(originalName, req.Prefix)
	case "suffix":
		return rc.applySuffixRename(originalName, req.Suffix)
	case "replace":
		return rc.applyReplaceRename(originalName, req.FindText, req.ReplaceText)
	case "mapping":
		return rc.applyMappingRename(originalName, req.MappingFrom, req.MappingTo)
	case "case":
		return rc.applyCaseRename(originalName, req.CaseType)
	case "extension":
		return rc.applyExtensionRename(originalName, req.NewExtension)
	default:
		return "", fmt.Errorf("unsupported rename type: %s", req.Type)
	}
}

// applyRegexRename 应用正则表达式重命名
func (rc *RenameController) applyRegexRename(originalName, pattern, replacement string) (string, error) {
	if pattern == "" {
		return "", fmt.Errorf("regex pattern cannot be empty")
	}

	regex, err := regexp.Compile(pattern)
	if err != nil {
		return "", fmt.Errorf("invalid regex pattern: %s", err.Error())
	}

	return regex.ReplaceAllString(originalName, replacement), nil
}

// applySequenceRename 应用序号重命名
func (rc *RenameController) applySequenceRename(originalName string, number int) (string, error) {
	// 获取文件扩展名
	parts := strings.Split(originalName, ".")
	if len(parts) > 1 {
		ext := parts[len(parts)-1]
		baseName := strings.Join(parts[:len(parts)-1], ".")
		return fmt.Sprintf("%s_%d.%s", baseName, number, ext), nil
	}
	return fmt.Sprintf("%s_%d", originalName, number), nil
}

// applyPrefixRename 应用前缀重命名
func (rc *RenameController) applyPrefixRename(originalName, prefix string) (string, error) {
	if prefix == "" {
		return "", fmt.Errorf("prefix cannot be empty")
	}
	return prefix + originalName, nil
}

// applySuffixRename 应用后缀重命名
func (rc *RenameController) applySuffixRename(originalName, suffix string) (string, error) {
	if suffix == "" {
		return "", fmt.Errorf("suffix cannot be empty")
	}

	// 获取文件扩展名
	parts := strings.Split(originalName, ".")
	if len(parts) > 1 {
		ext := parts[len(parts)-1]
		baseName := strings.Join(parts[:len(parts)-1], ".")
		return fmt.Sprintf("%s%s.%s", baseName, suffix, ext), nil
	}
	return originalName + suffix, nil
}

// applyCaseRename 应用大小写转换
func (rc *RenameController) applyCaseRename(originalName, caseType string) (string, error) {
	switch caseType {
	case "upper":
		return strings.ToUpper(originalName), nil
	case "lower":
		return strings.ToLower(originalName), nil
	case "title":
		return strings.Title(strings.ToLower(originalName)), nil
	default:
		return "", fmt.Errorf("unsupported case type: %s", caseType)
	}
}

// applyExtensionRename 应用扩展名替换
func (rc *RenameController) applyExtensionRename(originalName, newExtension string) (string, error) {
	if newExtension == "" {
		return "", fmt.Errorf("new extension cannot be empty")
	}

	// 清理新扩展名，移除开头的点号（如果有的话）
	newExtension = strings.TrimPrefix(newExtension, ".")

	// 获取文件名（不含扩展名）
	parts := strings.Split(originalName, ".")
	if len(parts) > 1 {
		// 有扩展名的情况，替换扩展名
		baseName := strings.Join(parts[:len(parts)-1], ".")
		return fmt.Sprintf("%s.%s", baseName, newExtension), nil
	}

	// 没有扩展名的情况，直接添加新扩展名
	return fmt.Sprintf("%s.%s", originalName, newExtension), nil
}

// applyReplaceRename 应用文本替换重命名
func (rc *RenameController) applyReplaceRename(originalName, findText, replaceText string) (string, error) {
	if findText == "" {
		return "", fmt.Errorf("find text cannot be empty")
	}

	// 添加调试日志
	fmt.Printf("DEBUG: applyReplaceRename - Original: '%s', Find: '%s', Replace: '%s'\n",
		originalName, findText, replaceText)

	// 使用strings.ReplaceAll进行全局替换
	newName := strings.ReplaceAll(originalName, findText, replaceText)

	// 添加调试日志
	fmt.Printf("DEBUG: applyReplaceRename - Result: '%s'\n", newName)

	// 如果没有发生任何替换，返回原始名称
	if newName == originalName {
		// 这里不返回错误，因为可能确实没有匹配的文本
		fmt.Printf("DEBUG: No replacement occurred\n")
		return originalName, nil
	}

	return newName, nil
}

// applyMappingRename 应用映射替换重命名
func (rc *RenameController) applyMappingRename(originalName, mappingFrom, mappingTo string) (string, error) {
	if mappingFrom == "" || mappingTo == "" {
		return "", fmt.Errorf("mapping lists cannot be empty")
	}

	// 解析映射列表
	fromList := rc.parseMappingList(mappingFrom)
	toList := rc.parseMappingList(mappingTo)

	// 添加调试日志
	fmt.Printf("DEBUG: applyMappingRename - Original: '%s'\n", originalName)
	fmt.Printf("DEBUG: FromList: %v (count: %d)\n", fromList, len(fromList))
	fmt.Printf("DEBUG: ToList: %v (count: %d)\n", toList, len(toList))

	// 检查数量是否匹配
	if len(fromList) != len(toList) {
		return "", fmt.Errorf("mapping lists must have the same length: from=%d, to=%d", len(fromList), len(toList))
	}

	if len(fromList) == 0 {
		return originalName, nil
	}

	// 创建映射关系
	mappingMap := make(map[string]string)
	for i, from := range fromList {
		mappingMap[from] = toList[i]
		fmt.Printf("DEBUG: Mapping[%d]: '%s' -> '%s'\n", i, from, toList[i])
	}

	// 在文件名中查找并替换匹配的数字
	newName := originalName
	replaced := false

	// 按照从长到短的顺序进行替换，避免部分匹配问题
	for from, to := range mappingMap {
		if strings.Contains(newName, from) {
			newName = strings.ReplaceAll(newName, from, to)
			fmt.Printf("DEBUG: Replaced '%s' with '%s', result: '%s'\n", from, to, newName)
			replaced = true
			break // 只替换第一个匹配的映射
		}
	}

	if !replaced {
		fmt.Printf("DEBUG: No mapping found for file: '%s'\n", originalName)
	}

	fmt.Printf("DEBUG: applyMappingRename - Final result: '%s'\n", newName)
	return newName, nil
}

// parseMappingList 解析映射列表
func (rc *RenameController) parseMappingList(text string) []string {
	if text == "" {
		return []string{}
	}

	// 支持换行符、空格和逗号分隔
	parts := strings.FieldsFunc(text, func(c rune) bool {
		return c == '\n' || c == ' ' || c == '\t' || c == ','
	})

	var result []string
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return result
}

// GetRenameOperations 获取重命名操作历史
func (rc *RenameController) GetRenameOperations(c *gin.Context) {
	userID := c.GetUint("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	offset := (page - 1) * limit

	var operations []models.RenameOperation
	var total int64

	rc.db.Model(&models.RenameOperation{}).Where("user_id = ?", userID).Count(&total)
	rc.db.Where("user_id = ?", userID).
		Preload("User").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&operations)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"operations": operations,
			"pagination": gin.H{
				"page":  page,
				"limit": limit,
				"total": total,
				"pages": (total + int64(limit) - 1) / int64(limit),
			},
		},
	})
}

// SaveRenameOperation 保存重命名操作模板
func (rc *RenameController) SaveRenameOperation(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req RenameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	operation := models.RenameOperation{
		Type:        req.Type,
		Pattern:     req.Pattern,
		Replacement: req.Replacement,
		Prefix:      req.Prefix,
		Suffix:      req.Suffix,
		CaseType:    req.CaseType,
		StartNumber: req.StartNumber,
		UserID:      userID,
	}

	if err := rc.db.Create(&operation).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to save rename operation",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Rename operation saved successfully",
		"data":    operation,
	})
}

// DeleteRenameOperation 删除重命名操作模板
func (rc *RenameController) DeleteRenameOperation(c *gin.Context) {
	userID := c.GetUint("user_id")
	operationID := c.Param("id")

	var operation models.RenameOperation
	if err := rc.db.Where("id = ? AND user_id = ?", operationID, userID).First(&operation).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Rename operation not found",
		})
		return
	}

	if err := rc.db.Delete(&operation).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete rename operation",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Rename operation deleted successfully",
	})
}
