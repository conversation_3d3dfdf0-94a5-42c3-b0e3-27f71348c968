package controllers

import (
	"encoding/csv"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"file-manager/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type LogController struct {
	db *gorm.DB
}

func NewLogController(db *gorm.DB) *LogController {
	return &LogController{db: db}
}

// GetLogs 获取操作日志列表
func (lc *LogController) GetLogs(c *gin.Context) {
	userID := c.GetUint("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "20"))
	action := c.Query("action")
	status := c.Query("status")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 从中间件获取当前用户信息（已验证）
	currentUser, exists := c.Get("current_user")
	if !exists {
		c.<PERSON><PERSON><PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "User context not found",
		})
		return
	}

	user := currentUser.(models.User)

	offset := (page - 1) * limit

	// 构建基础查询
	query := lc.db.Model(&models.OperationLog{})

	// 管理员可以查看所有日志，普通用户只能查看自己的日志
	if user.Role != "admin" {
		query = query.Where("user_id = ?", userID)
	}

	// 添加过滤条件
	if action != "" {
		query = query.Where("action = ?", action)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if startDate != "" {
		// 使用字符串匹配进行日期比较，兼容GORM的时间格式
		query = query.Where("created_at >= ?", startDate+" 00:00:00")
	}
	if endDate != "" {
		// 使用字符串匹配进行日期比较，兼容GORM的时间格式
		query = query.Where("created_at <= ?", endDate+" 23:59:59")
	}

	var logs []models.OperationLog
	var total int64

	// 先获取总数
	countQuery := *query
	countQuery.Model(&models.OperationLog{}).Count(&total)

	// 执行分页查询
	result := query.Preload("User").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&logs)

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch logs",
			"error":   result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"logs": logs,
			"pagination": gin.H{
				"page":  page,
				"limit": limit,
				"total": total,
				"pages": (total + int64(limit) - 1) / int64(limit),
			},
		},
	})
}

// GetLog 获取单个操作日志
func (lc *LogController) GetLog(c *gin.Context) {
	userID := c.GetUint("user_id")
	logID := c.Param("id")

	var log models.OperationLog
	if err := lc.db.Where("id = ? AND user_id = ?", logID, userID).
		Preload("User").
		First(&log).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Log not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    log,
	})
}

// DeleteLog 删除操作日志
func (lc *LogController) DeleteLog(c *gin.Context) {
	userID := c.GetUint("user_id")
	logID := c.Param("id")

	var log models.OperationLog
	if err := lc.db.Where("id = ? AND user_id = ?", logID, userID).First(&log).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Log not found",
		})
		return
	}

	if err := lc.db.Delete(&log).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete log",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Log deleted successfully",
	})
}

// ExportLogs 导出操作日志
func (lc *LogController) ExportLogs(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Action    string `json:"action"`
		Status    string `json:"status"`
		StartDate string `json:"start_date"`
		EndDate   string `json:"end_date"`
		Format    string `json:"format"` // csv, json
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	query := lc.db.Where("user_id = ?", userID)

	// 添加过滤条件
	if req.Action != "" {
		query = query.Where("action = ?", req.Action)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.StartDate != "" {
		// 使用字符串匹配进行日期比较，兼容GORM的时间格式
		query = query.Where("created_at >= ?", req.StartDate+" 00:00:00")
	}
	if req.EndDate != "" {
		// 使用字符串匹配进行日期比较，兼容GORM的时间格式
		query = query.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	var logs []models.OperationLog
	query.Preload("User").Order("created_at DESC").Find(&logs)

	// 根据格式导出
	switch req.Format {
	case "csv":
		lc.exportCSV(c, logs)
	case "json":
		lc.exportJSON(c, logs)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Unsupported export format",
		})
	}
}

// exportCSV 导出CSV格式
func (lc *LogController) exportCSV(c *gin.Context, logs []models.OperationLog) {
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=logs_%s.csv", time.Now().Format("20060102_150405")))

	writer := csv.NewWriter(c.Writer)
	defer writer.Flush()

	// 写入CSV头部
	headers := []string{"ID", "Action", "Resource", "Old Value", "New Value", "Status", "Error Message", "User", "Created At"}
	writer.Write(headers)

	// 写入数据
	for _, log := range logs {
		record := []string{
			strconv.FormatUint(uint64(log.ID), 10),
			log.Action,
			log.Resource,
			log.OldValue,
			log.NewValue,
			log.Status,
			log.ErrorMsg,
			log.User.Username,
			log.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		writer.Write(record)
	}
}

// exportJSON 导出JSON格式
func (lc *LogController) exportJSON(c *gin.Context, logs []models.OperationLog) {
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=logs_%s.json", time.Now().Format("20060102_150405")))

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"export_time": time.Now(),
		"total":       len(logs),
		"data":        logs,
	})
}

// CreateLog 创建操作日志（内部使用）
func (lc *LogController) CreateLog(userID uint, action, resource, oldValue, newValue, status, errorMsg string) error {
	log := models.OperationLog{
		Action:   action,
		Resource: resource,
		OldValue: oldValue,
		NewValue: newValue,
		Status:   status,
		ErrorMsg: errorMsg,
		UserID:   userID,
	}

	return lc.db.Create(&log).Error
}

// GetLogStats 获取日志统计信息
func (lc *LogController) GetLogStats(c *gin.Context) {
	userID := c.GetUint("user_id")

	// 从中间件获取当前用户信息（已验证）
	currentUser, exists := c.Get("current_user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "User context not found",
		})
		return
	}

	user := currentUser.(models.User)

	// 构建基础查询
	baseQuery := lc.db.Model(&models.OperationLog{})
	if user.Role != "admin" {
		baseQuery = baseQuery.Where("user_id = ?", userID)
	}

	// 统计各种操作的数量
	var stats []struct {
		Action string `json:"action"`
		Count  int64  `json:"count"`
	}

	baseQuery.Select("action, COUNT(*) as count").
		Group("action").
		Find(&stats)

	// 统计成功和失败的操作
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	baseQuery.Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats)

	// 获取最近7天的操作统计
	var dailyStats []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	dailyQuery := lc.db.Model(&models.OperationLog{}).
		Where("created_at >= ?", time.Now().AddDate(0, 0, -7))

	if user.Role != "admin" {
		dailyQuery = dailyQuery.Where("user_id = ?", userID)
	}

	dailyQuery.Select("DATE(created_at) as date, COUNT(*) as count").
		Group("DATE(created_at)").
		Order("date DESC").
		Find(&dailyStats)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"action_stats": stats,
			"status_stats": statusStats,
			"daily_stats":  dailyStats,
		},
	})
}

// BatchDeleteLogs 批量删除操作日志
func (lc *LogController) BatchDeleteLogs(c *gin.Context) {
	userID := c.GetUint("user_id")

	// 检查用户ID是否有效
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Unauthorized: Invalid user ID",
		})
		return
	}

	// 从中间件获取当前用户信息（已验证）
	currentUser, exists := c.Get("current_user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "User context not found",
		})
		return
	}

	user := currentUser.(models.User)

	var req struct {
		// 批量删除的方式
		DeleteType string `json:"delete_type" binding:"required,oneof=ids conditions all"`

		// 按ID删除
		LogIDs []uint `json:"log_ids"`

		// 按条件删除
		Action       string `json:"action"`
		Status       string `json:"status"`
		StartDate    string `json:"start_date"`
		EndDate      string `json:"end_date"`
		TargetUserID *uint  `json:"target_user_id"` // 管理员可以删除指定用户的日志

		// 确认删除（安全措施）
		Confirm bool `json:"confirm" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	if !req.Confirm {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Confirmation required for batch deletion",
		})
		return
	}

	// 构建查询条件
	query := lc.db.Model(&models.OperationLog{})

	// 权限检查：普通用户只能删除自己的日志
	if user.Role != "admin" {
		query = query.Where("user_id = ?", userID)
		if req.TargetUserID != nil && *req.TargetUserID != userID {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"message": "Access denied. Cannot delete other users' logs",
			})
			return
		}
	} else {
		// 管理员可以删除指定用户的日志
		if req.TargetUserID != nil {
			query = query.Where("user_id = ?", *req.TargetUserID)
		}
		// 如果没有指定用户，管理员默认删除自己的日志（安全考虑）
		if req.TargetUserID == nil && req.DeleteType != "all" {
			query = query.Where("user_id = ?", userID)
		}
	}

	// 根据删除类型添加条件
	switch req.DeleteType {
	case "ids":
		if len(req.LogIDs) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "No log IDs provided",
			})
			return
		}
		query = query.Where("id IN ?", req.LogIDs)

	case "conditions":
		// 添加筛选条件
		if req.Action != "" {
			query = query.Where("action = ?", req.Action)
		}
		if req.Status != "" {
			query = query.Where("status = ?", req.Status)
		}
		if req.StartDate != "" {
			// 使用字符串匹配进行日期比较，兼容GORM的时间格式
			query = query.Where("created_at >= ?", req.StartDate+" 00:00:00")
		}
		if req.EndDate != "" {
			// 使用字符串匹配进行日期比较，兼容GORM的时间格式
			query = query.Where("created_at <= ?", req.EndDate+" 23:59:59")
		}

	case "all":
		// 删除所有日志（仅管理员，且需要额外确认）
		if user.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"message": "Access denied. Admin role required for bulk deletion",
			})
			return
		}
		// 不添加额外条件，删除所有符合权限的日志
	}

	// 执行删除操作
	result := query.Delete(&models.OperationLog{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete logs",
			"error":   result.Error.Error(),
		})
		return
	}

	// 记录管理操作日志
	logMessage := fmt.Sprintf("Batch deleted %d operation logs", result.RowsAffected)
	if req.TargetUserID != nil && user.Role == "admin" {
		logMessage += fmt.Sprintf(" for user ID %d", *req.TargetUserID)
	}
	lc.CreateLog(userID, "batch_delete_logs", logMessage, "", fmt.Sprintf("%d logs deleted", result.RowsAffected), "success", "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Successfully deleted %d logs", result.RowsAffected),
		"data": gin.H{
			"deleted_count": result.RowsAffected,
			"delete_type":   req.DeleteType,
		},
	})
}

// CleanupLogs 清理旧日志
func (lc *LogController) CleanupLogs(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Days int `json:"days" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 删除指定天数之前的日志
	cutoffDate := time.Now().AddDate(0, 0, -req.Days)
	result := lc.db.Where("user_id = ? AND created_at < ?", userID, cutoffDate).
		Delete(&models.OperationLog{})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to cleanup logs",
			"error":   result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Successfully deleted %d old logs", result.RowsAffected),
		"data": gin.H{
			"deleted_count": result.RowsAffected,
			"cutoff_date":   cutoffDate,
		},
	})
}

// CleanupTestLogs 清理测试相关的日志数据
func (lc *LogController) CleanupTestLogs(c *gin.Context) {
	userID := c.GetUint("user_id")

	// 从中间件获取当前用户信息（已验证）
	currentUser, exists := c.Get("current_user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "User context not found",
		})
		return
	}

	user := currentUser.(models.User)

	if user.Role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "Admin role required for this operation",
		})
		return
	}

	// 删除所有测试相关的日志
	result := lc.db.Where("action LIKE ? OR resource LIKE ? OR old_value LIKE ? OR new_value LIKE ? OR action = ?",
		"%test%", "%test%", "%test%", "%test%", "batch_delete_logs").
		Delete(&models.OperationLog{})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to cleanup test logs",
			"error":   result.Error.Error(),
		})
		return
	}

	// 记录清理操作
	lc.CreateLog(userID, "cleanup_test_logs", "Test logs cleanup", "",
		fmt.Sprintf("%d test logs cleaned", result.RowsAffected), "success", "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Successfully cleaned up %d test logs", result.RowsAffected),
		"data": gin.H{
			"deleted_count": result.RowsAffected,
		},
	})
}
