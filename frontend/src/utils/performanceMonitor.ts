/**
 * 性能监控和内存管理工具
 * 用于监控应用性能、内存使用和自动优化
 */

// 性能指标接口
interface PerformanceMetrics {
  fps: number
  renderTime: number
  memoryUsage: number
  domNodes: number
  eventListeners: number
  timestamp: number
}

// 内存使用信息
interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  usage: number // 使用率百分比
}

// 性能警告类型
type PerformanceWarningType = 'low_fps' | 'high_memory' | 'slow_render' | 'too_many_dom_nodes'

interface PerformanceWarning {
  type: PerformanceWarningType
  message: string
  severity: 'low' | 'medium' | 'high'
  timestamp: number
  suggestions: string[]
}

// 性能配置
interface PerformanceConfig {
  fpsThreshold: number
  memoryThreshold: number
  renderTimeThreshold: number
  domNodeThreshold: number
  monitorInterval: number
  enableAutoOptimization: boolean
  enableWarnings: boolean
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private config: PerformanceConfig
  private metrics: PerformanceMetrics[] = []
  private warnings: PerformanceWarning[] = []
  private monitorInterval?: number
  private frameCount = 0
  private lastFrameTime = performance.now()
  private observers: Map<string, (metrics: PerformanceMetrics) => void> = new Map()
  private warningCallbacks: Map<string, (warning: PerformanceWarning) => void> = new Map()

  // 内存泄漏检测
  private memorySnapshots: MemoryInfo[] = []
  private leakDetectionInterval?: number

  // 自动优化策略
  private optimizationStrategies: Map<string, () => void> = new Map()

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      fpsThreshold: 30,
      memoryThreshold: 80, // 80% 内存使用率
      renderTimeThreshold: 16, // 16ms (60fps)
      domNodeThreshold: 5000,
      monitorInterval: 1000, // 1秒
      enableAutoOptimization: true,
      enableWarnings: true,
      ...config
    }

    this.initializeOptimizationStrategies()
    this.startMonitoring()
  }

  /**
   * 初始化优化策略
   */
  private initializeOptimizationStrategies(): void {
    this.optimizationStrategies.set('reduce_dom_nodes', () => {
      // 触发DOM节点清理
      this.triggerDOMCleanup()
    })

    this.optimizationStrategies.set('optimize_memory', () => {
      // 触发内存优化
      this.triggerMemoryOptimization()
    })

    this.optimizationStrategies.set('reduce_render_complexity', () => {
      // 触发渲染优化
      this.triggerRenderOptimization()
    })
  }

  /**
   * 开始性能监控
   */
  private startMonitoring(): void {
    this.monitorInterval = window.setInterval(() => {
      this.collectMetrics()
    }, this.config.monitorInterval)

    // 启动内存泄漏检测
    this.leakDetectionInterval = window.setInterval(() => {
      this.detectMemoryLeaks()
    }, 10000) // 每10秒检测一次

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    const now = performance.now()
    const memoryInfo = this.getMemoryInfo()
    
    const metrics: PerformanceMetrics = {
      fps: this.calculateFPS(),
      renderTime: this.getAverageRenderTime(),
      memoryUsage: memoryInfo.usage,
      domNodes: this.getDOMNodeCount(),
      eventListeners: this.getEventListenerCount(),
      timestamp: now
    }

    this.metrics.push(metrics)
    
    // 保持最近100个指标
    if (this.metrics.length > 100) {
      this.metrics.shift()
    }

    // 检查性能警告
    if (this.config.enableWarnings) {
      this.checkPerformanceWarnings(metrics)
    }

    // 自动优化
    if (this.config.enableAutoOptimization) {
      this.performAutoOptimization(metrics)
    }

    // 通知观察者
    this.notifyObservers(metrics)
  }

  /**
   * 计算FPS
   */
  private calculateFPS(): number {
    const now = performance.now()
    const delta = now - this.lastFrameTime
    
    if (delta >= 1000) {
      const fps = Math.round((this.frameCount * 1000) / delta)
      this.frameCount = 0
      this.lastFrameTime = now
      return fps
    } else {
      this.frameCount++
      return 60 // 默认返回60fps
    }
  }

  /**
   * 获取平均渲染时间
   */
  private getAverageRenderTime(): number {
    const entries = performance.getEntriesByType('measure')
    const renderEntries = entries.filter(entry => entry.name.includes('render'))
    
    if (renderEntries.length === 0) return 0
    
    const totalTime = renderEntries.reduce((sum, entry) => sum + entry.duration, 0)
    return totalTime / renderEntries.length
  }

  /**
   * 获取内存信息
   */
  private getMemoryInfo(): MemoryInfo {
    const memory = (performance as any).memory
    
    if (!memory) {
      return {
        usedJSHeapSize: 0,
        totalJSHeapSize: 0,
        jsHeapSizeLimit: 0,
        usage: 0
      }
    }

    const usage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100

    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usage
    }
  }

  /**
   * 获取DOM节点数量
   */
  private getDOMNodeCount(): number {
    return document.querySelectorAll('*').length
  }

  /**
   * 获取事件监听器数量（估算）
   */
  private getEventListenerCount(): number {
    // 这是一个估算值，实际实现可能需要更复杂的逻辑
    const elements = document.querySelectorAll('*')
    let count = 0
    
    elements.forEach(element => {
      // 检查常见的事件属性
      const eventAttributes = ['onclick', 'onmouseover', 'onmouseout', 'onkeydown', 'onkeyup']
      eventAttributes.forEach(attr => {
        if (element.hasAttribute(attr)) {
          count++
        }
      })
    })
    
    return count
  }

  /**
   * 检查性能警告
   */
  private checkPerformanceWarnings(metrics: PerformanceMetrics): void {
    const warnings: PerformanceWarning[] = []

    // FPS过低警告
    if (metrics.fps < this.config.fpsThreshold) {
      warnings.push({
        type: 'low_fps',
        message: `FPS过低: ${metrics.fps}fps (阈值: ${this.config.fpsThreshold}fps)`,
        severity: metrics.fps < 15 ? 'high' : 'medium',
        timestamp: metrics.timestamp,
        suggestions: [
          '减少DOM操作频率',
          '使用虚拟滚动',
          '优化CSS动画',
          '减少重绘和重排'
        ]
      })
    }

    // 内存使用过高警告
    if (metrics.memoryUsage > this.config.memoryThreshold) {
      warnings.push({
        type: 'high_memory',
        message: `内存使用过高: ${metrics.memoryUsage.toFixed(1)}% (阈值: ${this.config.memoryThreshold}%)`,
        severity: metrics.memoryUsage > 90 ? 'high' : 'medium',
        timestamp: metrics.timestamp,
        suggestions: [
          '清理未使用的对象引用',
          '减少缓存数据',
          '使用对象池',
          '检查内存泄漏'
        ]
      })
    }

    // 渲染时间过长警告
    if (metrics.renderTime > this.config.renderTimeThreshold) {
      warnings.push({
        type: 'slow_render',
        message: `渲染时间过长: ${metrics.renderTime.toFixed(1)}ms (阈值: ${this.config.renderTimeThreshold}ms)`,
        severity: metrics.renderTime > 32 ? 'high' : 'medium',
        timestamp: metrics.timestamp,
        suggestions: [
          '使用时间切片',
          '减少组件复杂度',
          '优化计算逻辑',
          '使用Web Workers'
        ]
      })
    }

    // DOM节点过多警告
    if (metrics.domNodes > this.config.domNodeThreshold) {
      warnings.push({
        type: 'too_many_dom_nodes',
        message: `DOM节点过多: ${metrics.domNodes} (阈值: ${this.config.domNodeThreshold})`,
        severity: metrics.domNodes > 10000 ? 'high' : 'medium',
        timestamp: metrics.timestamp,
        suggestions: [
          '使用虚拟滚动',
          '延迟加载非关键元素',
          '清理未使用的DOM节点',
          '使用文档片段'
        ]
      })
    }

    // 添加新警告并通知
    warnings.forEach(warning => {
      this.warnings.push(warning)
      this.notifyWarningCallbacks(warning)
    })

    // 保持最近50个警告
    if (this.warnings.length > 50) {
      this.warnings = this.warnings.slice(-50)
    }
  }

  /**
   * 执行自动优化
   */
  private performAutoOptimization(metrics: PerformanceMetrics): void {
    // 内存优化
    if (metrics.memoryUsage > this.config.memoryThreshold) {
      this.optimizationStrategies.get('optimize_memory')?.()
    }

    // DOM节点优化
    if (metrics.domNodes > this.config.domNodeThreshold) {
      this.optimizationStrategies.get('reduce_dom_nodes')?.()
    }

    // 渲染优化
    if (metrics.renderTime > this.config.renderTimeThreshold) {
      this.optimizationStrategies.get('reduce_render_complexity')?.()
    }
  }

  /**
   * 检测内存泄漏
   */
  private detectMemoryLeaks(): void {
    const memoryInfo = this.getMemoryInfo()
    this.memorySnapshots.push(memoryInfo)

    // 保持最近10个快照
    if (this.memorySnapshots.length > 10) {
      this.memorySnapshots.shift()
    }

    // 检测内存持续增长
    if (this.memorySnapshots.length >= 5) {
      const recent = this.memorySnapshots.slice(-5)
      const isIncreasing = recent.every((snapshot, index) => {
        return index === 0 || snapshot.usedJSHeapSize > recent[index - 1].usedJSHeapSize
      })

      if (isIncreasing) {
        const growth = recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize
        const growthMB = growth / (1024 * 1024)

        if (growthMB > 10) { // 增长超过10MB
          console.warn(`检测到可能的内存泄漏: ${growthMB.toFixed(1)}MB 增长`)
          
          // 触发内存清理
          this.triggerMemoryOptimization()
        }
      }
    }
  }

  /**
   * 触发DOM清理
   */
  private triggerDOMCleanup(): void {
    // 发出DOM清理事件
    window.dispatchEvent(new CustomEvent('performance:dom-cleanup'))
  }

  /**
   * 触发内存优化
   */
  private triggerMemoryOptimization(): void {
    // 强制垃圾回收（如果可用）
    if ('gc' in window) {
      (window as any).gc()
    }

    // 发出内存优化事件
    window.dispatchEvent(new CustomEvent('performance:memory-optimize'))
  }

  /**
   * 触发渲染优化
   */
  private triggerRenderOptimization(): void {
    // 发出渲染优化事件
    window.dispatchEvent(new CustomEvent('performance:render-optimize'))
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      // 页面隐藏时暂停监控
      this.pauseMonitoring()
    } else {
      // 页面显示时恢复监控
      this.resumeMonitoring()
    }
  }

  /**
   * 暂停监控
   */
  public pauseMonitoring(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = undefined
    }
    
    if (this.leakDetectionInterval) {
      clearInterval(this.leakDetectionInterval)
      this.leakDetectionInterval = undefined
    }
  }

  /**
   * 恢复监控
   */
  public resumeMonitoring(): void {
    if (!this.monitorInterval) {
      this.startMonitoring()
    }
  }

  /**
   * 添加性能指标观察者
   */
  public addObserver(id: string, callback: (metrics: PerformanceMetrics) => void): void {
    this.observers.set(id, callback)
  }

  /**
   * 移除性能指标观察者
   */
  public removeObserver(id: string): void {
    this.observers.delete(id)
  }

  /**
   * 添加警告回调
   */
  public addWarningCallback(id: string, callback: (warning: PerformanceWarning) => void): void {
    this.warningCallbacks.set(id, callback)
  }

  /**
   * 移除警告回调
   */
  public removeWarningCallback(id: string): void {
    this.warningCallbacks.delete(id)
  }

  /**
   * 通知观察者
   */
  private notifyObservers(metrics: PerformanceMetrics): void {
    this.observers.forEach(callback => {
      try {
        callback(metrics)
      } catch (error) {
        console.error('Performance observer error:', error)
      }
    })
  }

  /**
   * 通知警告回调
   */
  private notifyWarningCallbacks(warning: PerformanceWarning): void {
    this.warningCallbacks.forEach(callback => {
      try {
        callback(warning)
      } catch (error) {
        console.error('Performance warning callback error:', error)
      }
    })
  }

  /**
   * 获取当前性能指标
   */
  public getCurrentMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  /**
   * 获取历史性能指标
   */
  public getHistoryMetrics(count: number = 10): PerformanceMetrics[] {
    return this.metrics.slice(-count)
  }

  /**
   * 获取性能警告
   */
  public getWarnings(count: number = 10): PerformanceWarning[] {
    return this.warnings.slice(-count)
  }

  /**
   * 清除警告
   */
  public clearWarnings(): void {
    this.warnings.length = 0
  }

  /**
   * 获取性能报告
   */
  public getPerformanceReport(): {
    current: PerformanceMetrics | null
    average: Partial<PerformanceMetrics>
    warnings: PerformanceWarning[]
    memoryTrend: 'stable' | 'increasing' | 'decreasing'
  } {
    const current = this.getCurrentMetrics()
    const history = this.getHistoryMetrics(10)
    
    // 计算平均值
    const average: Partial<PerformanceMetrics> = {}
    if (history.length > 0) {
      average.fps = history.reduce((sum, m) => sum + m.fps, 0) / history.length
      average.renderTime = history.reduce((sum, m) => sum + m.renderTime, 0) / history.length
      average.memoryUsage = history.reduce((sum, m) => sum + m.memoryUsage, 0) / history.length
      average.domNodes = history.reduce((sum, m) => sum + m.domNodes, 0) / history.length
    }

    // 分析内存趋势
    let memoryTrend: 'stable' | 'increasing' | 'decreasing' = 'stable'
    if (this.memorySnapshots.length >= 3) {
      const recent = this.memorySnapshots.slice(-3)
      const first = recent[0].usedJSHeapSize
      const last = recent[recent.length - 1].usedJSHeapSize
      const diff = last - first
      
      if (Math.abs(diff) > 1024 * 1024) { // 1MB差异
        memoryTrend = diff > 0 ? 'increasing' : 'decreasing'
      }
    }

    return {
      current,
      average,
      warnings: this.getWarnings(),
      memoryTrend
    }
  }

  /**
   * 销毁监控器
   */
  public destroy(): void {
    this.pauseMonitoring()
    
    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
    
    this.observers.clear()
    this.warningCallbacks.clear()
    this.optimizationStrategies.clear()
    this.metrics.length = 0
    this.warnings.length = 0
    this.memorySnapshots.length = 0
  }
}

// 单例实例
let performanceMonitorInstance: PerformanceMonitor | null = null

/**
 * 获取性能监控器单例
 */
export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitorInstance) {
    performanceMonitorInstance = new PerformanceMonitor()
  }
  return performanceMonitorInstance
}

/**
 * 销毁性能监控器单例
 */
export function destroyPerformanceMonitor(): void {
  if (performanceMonitorInstance) {
    performanceMonitorInstance.destroy()
    performanceMonitorInstance = null
  }
}

// 导出类型
export type {
  PerformanceMetrics,
  MemoryInfo,
  PerformanceWarning,
  PerformanceWarningType,
  PerformanceConfig
}