/**
 * Web Worker 管理器
 * 负责管理 Worker 实例、任务队列和通信
 */

import type { FileItem, RenameConfig, PreviewResult, PerformanceMetrics } from '@/workers/renameWorker'

// Worker 任务状态
type TaskStatus = 'pending' | 'running' | 'completed' | 'error'

// Worker 任务接口
interface WorkerTask {
  id: string
  type: string
  status: TaskStatus
  startTime: number
  endTime?: number
  resolve: (value: any) => void
  reject: (error: any) => void
  onProgress?: (progress: number, data?: any) => void
  [key: string]: any // 允许额外的属性
}

// Worker 池配置
interface WorkerPoolConfig {
  maxWorkers: number
  taskTimeout: number
  enableMemoryMonitoring: boolean
}

// 性能统计
interface WorkerStats {
  totalTasks: number
  completedTasks: number
  failedTasks: number
  averageTaskTime: number
  totalComputeTime: number
  memoryUsage: number
}

/**
 * Web Worker 管理器类
 */
export class WorkerManager {
  private workers: Worker[] = []
  private availableWorkers: Worker[] = []
  private tasks = new Map<string, WorkerTask>()
  private taskQueue: WorkerTask[] = []
  private config: WorkerPoolConfig
  private stats: WorkerStats = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    averageTaskTime: 0,
    totalComputeTime: 0,
    memoryUsage: 0
  }
  private memoryMonitorInterval?: number

  constructor(config: Partial<WorkerPoolConfig> = {}) {
    this.config = {
      maxWorkers: Math.min(navigator.hardwareConcurrency || 4, 8), // 最多8个Worker
      taskTimeout: 30000, // 30秒超时
      enableMemoryMonitoring: true,
      ...config
    }

    this.initializeWorkers()
    
    if (this.config.enableMemoryMonitoring) {
      this.startMemoryMonitoring()
    }
  }

  /**
   * 初始化 Worker 池
   */
  private initializeWorkers(): void {
    for (let i = 0; i < this.config.maxWorkers; i++) {
      try {
        const worker = new Worker(
          new URL('../workers/renameWorker.ts', import.meta.url),
          { type: 'module' }
        )
        
        worker.onmessage = this.handleWorkerMessage.bind(this)
        worker.onerror = this.handleWorkerError.bind(this)
        
        this.workers.push(worker)
        this.availableWorkers.push(worker)
      } catch (error) {
        console.error('Failed to create worker:', error)
      }
    }
    
    console.log(`Initialized ${this.workers.length} workers`)
  }

  /**
   * 处理 Worker 消息
   */
  private handleWorkerMessage(event: MessageEvent): void {
    const { type, data } = event.data
    
    switch (type) {
      case 'COMPUTE_COMPLETE':
      case 'INCREMENTAL_COMPLETE':
      case 'VALIDATION_COMPLETE':
        this.handleTaskComplete(data.id, data)
        break
        
      case 'PROGRESS':
        this.handleTaskProgress(data.id, data.progress, data.partialResults)
        break
        
      case 'ERROR':
        this.handleTaskError(data.id, data.error)
        break
        
      case 'MEMORY_USAGE':
        this.stats.memoryUsage = data.memoryUsage
        break
        
      default:
        console.warn('Unknown worker message type:', type)
    }
  }

  /**
   * 处理 Worker 错误
   */
  private handleWorkerError(error: ErrorEvent): void {
    console.error('Worker error:', error)
    // 可以在这里实现 Worker 重启逻辑
  }

  /**
   * 处理任务完成
   */
  private handleTaskComplete(taskId: string, result: any): void {
    const task = this.tasks.get(taskId)
    if (!task) return

    task.status = 'completed'
    task.endTime = performance.now()
    
    // 更新统计信息
    this.stats.completedTasks++
    const taskTime = task.endTime - task.startTime
    this.stats.totalComputeTime += taskTime
    this.stats.averageTaskTime = this.stats.totalComputeTime / this.stats.completedTasks

    // 释放 Worker
    const worker = this.findWorkerByTask(taskId)
    if (worker) {
      this.availableWorkers.push(worker)
    }

    task.resolve(result)
    this.tasks.delete(taskId)
    
    // 处理队列中的下一个任务
    this.processNextTask()
  }

  /**
   * 处理任务进度
   */
  private handleTaskProgress(taskId: string, progress: number, data?: any): void {
    const task = this.tasks.get(taskId)
    if (task && task.onProgress) {
      task.onProgress(progress, data)
    }
  }

  /**
   * 处理任务错误
   */
  private handleTaskError(taskId: string, error: string): void {
    const task = this.tasks.get(taskId)
    if (!task) return

    task.status = 'error'
    task.endTime = performance.now()
    
    this.stats.failedTasks++

    // 释放 Worker
    const worker = this.findWorkerByTask(taskId)
    if (worker) {
      this.availableWorkers.push(worker)
    }

    task.reject(new Error(error))
    this.tasks.delete(taskId)
    
    // 处理队列中的下一个任务
    this.processNextTask()
  }

  /**
   * 查找执行特定任务的 Worker
   */
  private findWorkerByTask(taskId: string): Worker | null {
    // 简化实现：返回第一个不在可用列表中的 Worker
    return this.workers.find(worker => !this.availableWorkers.includes(worker)) || null
  }

  /**
   * 处理队列中的下一个任务
   */
  private processNextTask(): void {
    if (this.taskQueue.length === 0 || this.availableWorkers.length === 0) {
      return
    }

    const task = this.taskQueue.shift()!
    const worker = this.availableWorkers.shift()!
    
    task.status = 'running'
    task.startTime = performance.now()
    
    // 设置任务超时
    setTimeout(() => {
      if (task.status === 'running') {
        this.handleTaskError(task.id, 'Task timeout')
      }
    }, this.config.taskTimeout)

    // 发送任务到 Worker
    const { id, type, status, startTime, endTime, resolve, reject, onProgress, ...taskData } = task
    worker.postMessage({
      type: task.type,
      data: { id: task.id, ...taskData }
    })
  }

  /**
   * 生成唯一任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 计算重命名结果
   */
  public async computeRename(
    files: FileItem[],
    config: RenameConfig,
    options: {
      batchSize?: number
      onProgress?: (progress: number, partialResults?: PreviewResult[]) => void
    } = {}
  ): Promise<{ results: PreviewResult[], metrics: PerformanceMetrics }> {
    return new Promise((resolve, reject) => {
      const taskId = this.generateTaskId()
      const task: WorkerTask = {
        id: taskId,
        type: 'COMPUTE_RENAME',
        status: 'pending',
        startTime: 0,
        resolve,
        reject,
        onProgress: options.onProgress,
        files,
        config,
        batchSize: options.batchSize || this.calculateOptimalBatchSize(files.length)
      }

      this.stats.totalTasks++
      this.tasks.set(taskId, task)

      if (this.availableWorkers.length > 0) {
        this.processNextTask()
      } else {
        this.taskQueue.push(task)
      }
    })
  }

  /**
   * 增量计算重命名结果
   */
  public async computeIncremental(
    files: FileItem[],
    config: RenameConfig,
    previousResults: PreviewResult[],
    changedIndices: number[]
  ): Promise<{ results: PreviewResult[], computeTime: number, changedCount: number }> {
    return new Promise((resolve, reject) => {
      const taskId = this.generateTaskId()
      const task: WorkerTask = {
        id: taskId,
        type: 'COMPUTE_INCREMENTAL',
        status: 'pending',
        startTime: 0,
        resolve,
        reject,
        files,
        config,
        previousResults,
        changedIndices
      }

      this.stats.totalTasks++
      this.tasks.set(taskId, task)

      if (this.availableWorkers.length > 0) {
        this.processNextTask()
      } else {
        this.taskQueue.push(task)
      }
    })
  }

  /**
   * 验证重命名配置
   */
  public async validateConfig(config: RenameConfig): Promise<{ isValid: boolean, errors: string[] }> {
    return new Promise((resolve, reject) => {
      const taskId = this.generateTaskId()
      const task: WorkerTask = {
        id: taskId,
        type: 'VALIDATE_CONFIG',
        status: 'pending',
        startTime: 0,
        resolve,
        reject,
        config
      }

      this.tasks.set(taskId, task)

      if (this.availableWorkers.length > 0) {
        this.processNextTask()
      } else {
        this.taskQueue.push(task)
      }
    })
  }

  /**
   * 计算最优批次大小
   */
  private calculateOptimalBatchSize(fileCount: number): number {
    // 根据文件数量和设备性能动态调整批次大小
    const deviceMemory = (navigator as any).deviceMemory || 4 // GB
    const hardwareConcurrency = navigator.hardwareConcurrency || 4
    
    let batchSize = 100 // 默认批次大小
    
    if (fileCount > 10000) {
      // 大量文件：减小批次大小以避免内存压力
      batchSize = Math.max(50, Math.min(200, Math.floor(fileCount / 100)))
    } else if (fileCount > 1000) {
      // 中等数量：平衡性能和内存
      batchSize = Math.max(100, Math.min(500, Math.floor(fileCount / 20)))
    } else {
      // 少量文件：可以使用较大批次
      batchSize = Math.min(fileCount, 200)
    }
    
    // 根据设备性能调整
    if (deviceMemory >= 8 && hardwareConcurrency >= 8) {
      batchSize = Math.floor(batchSize * 1.5) // 高性能设备增加批次大小
    } else if (deviceMemory <= 2 || hardwareConcurrency <= 2) {
      batchSize = Math.floor(batchSize * 0.5) // 低性能设备减小批次大小
    }
    
    return Math.max(10, batchSize) // 最小批次大小为10
  }

  /**
   * 开始内存监控
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitorInterval = window.setInterval(() => {
      if (this.availableWorkers.length < this.workers.length) {
        // 有任务在运行时才监控内存
        this.workers.forEach(worker => {
          worker.postMessage({ type: 'GET_MEMORY_USAGE' })
        })
      }
    }, 5000) // 每5秒监控一次
  }

  /**
   * 获取性能统计
   */
  public getStats(): WorkerStats {
    return { ...this.stats }
  }

  /**
   * 获取当前状态
   */
  public getStatus(): {
    totalWorkers: number
    availableWorkers: number
    runningTasks: number
    queuedTasks: number
  } {
    return {
      totalWorkers: this.workers.length,
      availableWorkers: this.availableWorkers.length,
      runningTasks: this.tasks.size,
      queuedTasks: this.taskQueue.length
    }
  }

  /**
   * 取消所有任务
   */
  public cancelAllTasks(): void {
    // 取消队列中的任务
    this.taskQueue.forEach(task => {
      task.reject(new Error('Task cancelled'))
    })
    this.taskQueue.length = 0

    // 取消正在运行的任务
    this.tasks.forEach(task => {
      if (task.status === 'running') {
        task.reject(new Error('Task cancelled'))
      }
    })
    this.tasks.clear()

    // 重置 Worker 可用状态
    this.availableWorkers = [...this.workers]
  }

  /**
   * 销毁 Worker 管理器
   */
  public destroy(): void {
    this.cancelAllTasks()
    
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval)
    }

    this.workers.forEach(worker => {
      worker.terminate()
    })
    
    this.workers.length = 0
    this.availableWorkers.length = 0
  }
}

// 单例实例
let workerManagerInstance: WorkerManager | null = null

/**
 * 获取 Worker 管理器单例
 */
export function getWorkerManager(): WorkerManager {
  if (!workerManagerInstance) {
    workerManagerInstance = new WorkerManager()
  }
  return workerManagerInstance
}

/**
 * 销毁 Worker 管理器单例
 */
export function destroyWorkerManager(): void {
  if (workerManagerInstance) {
    workerManagerInstance.destroy()
    workerManagerInstance = null
  }
}