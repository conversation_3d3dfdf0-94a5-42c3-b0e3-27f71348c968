/**
 * 时间切片渲染工具
 * 用于将大量计算任务分解为小块，避免阻塞主线程
 */

// 任务优先级
export enum TaskPriority {
  IMMEDIATE = 1,    // 立即执行
  HIGH = 2,         // 高优先级
  NORMAL = 3,       // 普通优先级
  LOW = 4,          // 低优先级
  IDLE = 5          // 空闲时执行
}

// 时间切片任务接口
export interface TimeSliceTask<T = any> {
  id: string
  priority: TaskPriority
  execute: () => T | Promise<T>
  onComplete?: (result: T) => void
  onError?: (error: Error) => void
  timeout?: number
  createdAt: number
}

// 批处理任务接口
export interface BatchTask<T = any, R = any> {
  id: string
  priority: TaskPriority
  items: T[]
  processor: (item: T, index: number) => R | Promise<R>
  batchSize?: number
  onProgress?: (completed: number, total: number, results: R[]) => void
  onComplete?: (results: R[]) => void
  onError?: (error: Error, failedIndex: number) => void
  timeout?: number
  createdAt: number
}

// 调度器配置
interface SchedulerConfig {
  maxExecutionTime: number    // 每个时间片最大执行时间（毫秒）
  idleTimeout: number         // 空闲检测超时时间
  maxConcurrentTasks: number  // 最大并发任务数
  enableProfiling: boolean    // 启用性能分析
}

// 性能分析数据
interface ProfilingData {
  taskId: string
  priority: TaskPriority
  executionTime: number
  queueTime: number
  memoryUsage: number
  timestamp: number
}

/**
 * 时间切片调度器
 */
export class TimeSliceScheduler {
  private config: SchedulerConfig
  private taskQueue: Map<TaskPriority, TimeSliceTask[]> = new Map()
  private batchQueue: Map<TaskPriority, BatchTask[]> = new Map()
  private runningTasks = new Set<string>()
  private isRunning = false
  private frameId?: number
  private profilingData: ProfilingData[] = []

  // 性能监控
  private lastFrameTime = performance.now()
  private frameCount = 0
  private averageFrameTime = 16.67 // 60fps

  constructor(config: Partial<SchedulerConfig> = {}) {
    this.config = {
      maxExecutionTime: 5, // 5ms时间片
      idleTimeout: 50,
      maxConcurrentTasks: 3,
      enableProfiling: false,
      ...config
    }

    // 初始化优先级队列
    Object.values(TaskPriority).forEach(priority => {
      if (typeof priority === 'number') {
        this.taskQueue.set(priority, [])
        this.batchQueue.set(priority, [])
      }
    })

    this.startScheduler()
  }

  /**
   * 启动调度器
   */
  private startScheduler(): void {
    if (this.isRunning) return
    
    this.isRunning = true
    this.scheduleNextFrame()
  }

  /**
   * 调度下一帧
   */
  private scheduleNextFrame(): void {
    if (!this.isRunning) return

    this.frameId = requestAnimationFrame((timestamp) => {
      this.updateFrameStats(timestamp)
      this.processTaskQueue()
      this.scheduleNextFrame()
    })
  }

  /**
   * 更新帧统计
   */
  private updateFrameStats(timestamp: number): void {
    const deltaTime = timestamp - this.lastFrameTime
    this.lastFrameTime = timestamp
    this.frameCount++

    // 计算平均帧时间
    this.averageFrameTime = (this.averageFrameTime * 0.9) + (deltaTime * 0.1)

    // 根据帧率动态调整时间片大小
    if (this.averageFrameTime > 20) { // 低于50fps
      this.config.maxExecutionTime = Math.max(2, this.config.maxExecutionTime - 1)
    } else if (this.averageFrameTime < 14) { // 高于70fps
      this.config.maxExecutionTime = Math.min(10, this.config.maxExecutionTime + 1)
    }
  }

  /**
   * 处理任务队列
   */
  private processTaskQueue(): void {
    const startTime = performance.now()
    const maxTime = this.config.maxExecutionTime

    // 按优先级处理任务
    for (const priority of [TaskPriority.IMMEDIATE, TaskPriority.HIGH, TaskPriority.NORMAL, TaskPriority.LOW, TaskPriority.IDLE]) {
      if (performance.now() - startTime >= maxTime) break
      if (this.runningTasks.size >= this.config.maxConcurrentTasks) break

      // 处理单个任务
      this.processSingleTasks(priority, startTime, maxTime)
      
      // 处理批量任务
      this.processBatchTasks(priority, startTime, maxTime)
    }
  }

  /**
   * 处理单个任务
   */
  private processSingleTasks(priority: TaskPriority, startTime: number, maxTime: number): void {
    const tasks = this.taskQueue.get(priority) || []
    
    while (tasks.length > 0 && performance.now() - startTime < maxTime) {
      if (this.runningTasks.size >= this.config.maxConcurrentTasks) break

      const task = tasks.shift()!
      this.executeTask(task)
    }
  }

  /**
   * 处理批量任务
   */
  private processBatchTasks(priority: TaskPriority, startTime: number, maxTime: number): void {
    const batches = this.batchQueue.get(priority) || []
    
    for (let i = 0; i < batches.length; i++) {
      if (performance.now() - startTime >= maxTime) break
      if (this.runningTasks.size >= this.config.maxConcurrentTasks) break

      const batch = batches[i]
      const processed = this.processBatchSlice(batch, startTime, maxTime)
      
      if (processed) {
        batches.splice(i, 1)
        i-- // 调整索引
      }
    }
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: TimeSliceTask): Promise<void> {
    const taskStartTime = performance.now()
    const queueTime = taskStartTime - task.createdAt

    this.runningTasks.add(task.id)

    try {
      // 设置超时
      const timeoutPromise = task.timeout ? 
        new Promise((_, reject) => setTimeout(() => reject(new Error('Task timeout')), task.timeout)) :
        null

      const taskPromise = Promise.resolve(task.execute())
      const result = timeoutPromise ? 
        await Promise.race([taskPromise, timeoutPromise]) :
        await taskPromise

      const executionTime = performance.now() - taskStartTime

      // 记录性能数据
      if (this.config.enableProfiling) {
        this.recordProfilingData(task, executionTime, queueTime)
      }

      task.onComplete?.(result)
    } catch (error) {
      task.onError?.(error as Error)
    } finally {
      this.runningTasks.delete(task.id)
    }
  }

  /**
   * 处理批量任务切片
   */
  private processBatchSlice(batch: BatchTask, startTime: number, maxTime: number): boolean {
    const batchSize = batch.batchSize || 10
    const results: any[] = []
    let processed = 0
    let completed = false

    // 获取或初始化批量任务状态
    const batchState = this.getBatchState(batch.id)
    
    while (batchState.currentIndex < batch.items.length && 
           performance.now() - startTime < maxTime &&
           processed < batchSize) {
      
      const item = batch.items[batchState.currentIndex]
      
      try {
        const result = batch.processor(item, batchState.currentIndex)
        batchState.results.push(result)
        processed++
        batchState.currentIndex++
      } catch (error) {
        batch.onError?.(error as Error, batchState.currentIndex)
        batchState.currentIndex++
      }
    }

    // 报告进度
    if (batch.onProgress) {
      batch.onProgress(batchState.currentIndex, batch.items.length, batchState.results)
    }

    // 检查是否完成
    if (batchState.currentIndex >= batch.items.length) {
      batch.onComplete?.(batchState.results)
      this.deleteBatchState(batch.id)
      completed = true
    }

    return completed
  }

  /**
   * 批量任务状态管理
   */
  private batchStates = new Map<string, { currentIndex: number, results: any[] }>()

  private getBatchState(batchId: string) {
    if (!this.batchStates.has(batchId)) {
      this.batchStates.set(batchId, { currentIndex: 0, results: [] })
    }
    return this.batchStates.get(batchId)!
  }

  private deleteBatchState(batchId: string) {
    this.batchStates.delete(batchId)
  }

  /**
   * 记录性能分析数据
   */
  private recordProfilingData(task: TimeSliceTask, executionTime: number, queueTime: number): void {
    const memoryUsage = (performance as any).memory?.usedJSHeapSize || 0

    this.profilingData.push({
      taskId: task.id,
      priority: task.priority,
      executionTime,
      queueTime,
      memoryUsage,
      timestamp: performance.now()
    })

    // 保持最近1000条记录
    if (this.profilingData.length > 1000) {
      this.profilingData.shift()
    }
  }

  /**
   * 添加单个任务
   */
  public addTask<T>(
    execute: () => T | Promise<T>,
    options: {
      priority?: TaskPriority
      onComplete?: (result: T) => void
      onError?: (error: Error) => void
      timeout?: number
    } = {}
  ): string {
    const taskId = this.generateTaskId()
    const task: TimeSliceTask<T> = {
      id: taskId,
      priority: options.priority || TaskPriority.NORMAL,
      execute,
      onComplete: options.onComplete,
      onError: options.onError,
      timeout: options.timeout,
      createdAt: performance.now()
    }

    const queue = this.taskQueue.get(task.priority)!
    queue.push(task)

    return taskId
  }

  /**
   * 添加批量任务
   */
  public addBatchTask<T, R>(
    items: T[],
    processor: (item: T, index: number) => R | Promise<R>,
    options: {
      priority?: TaskPriority
      batchSize?: number
      onProgress?: (completed: number, total: number, results: R[]) => void
      onComplete?: (results: R[]) => void
      onError?: (error: Error, failedIndex: number) => void
      timeout?: number
    } = {}
  ): string {
    const batchId = this.generateTaskId()
    const batch: BatchTask<T, R> = {
      id: batchId,
      priority: options.priority || TaskPriority.NORMAL,
      items,
      processor,
      batchSize: options.batchSize,
      onProgress: options.onProgress,
      onComplete: options.onComplete,
      onError: options.onError,
      timeout: options.timeout,
      createdAt: performance.now()
    }

    const queue = this.batchQueue.get(batch.priority)!
    queue.push(batch)

    return batchId
  }

  /**
   * 取消任务
   */
  public cancelTask(taskId: string): boolean {
    // 从队列中移除
    for (const queue of this.taskQueue.values()) {
      const index = queue.findIndex(task => task.id === taskId)
      if (index !== -1) {
        queue.splice(index, 1)
        return true
      }
    }

    // 从批量队列中移除
    for (const queue of this.batchQueue.values()) {
      const index = queue.findIndex(batch => batch.id === taskId)
      if (index !== -1) {
        queue.splice(index, 1)
        this.deleteBatchState(taskId)
        return true
      }
    }

    return false
  }

  /**
   * 清空所有任务
   */
  public clearAllTasks(): void {
    this.taskQueue.forEach(queue => queue.length = 0)
    this.batchQueue.forEach(queue => queue.length = 0)
    this.batchStates.clear()
  }

  /**
   * 获取队列状态
   */
  public getQueueStatus(): {
    totalTasks: number
    runningTasks: number
    tasksByPriority: Record<TaskPriority, number>
    averageFrameTime: number
    currentTimeSlice: number
  } {
    let totalTasks = 0
    const tasksByPriority: Record<TaskPriority, number> = {} as any

    // 统计单个任务
    this.taskQueue.forEach((queue, priority) => {
      const count = queue.length
      totalTasks += count
      tasksByPriority[priority] = (tasksByPriority[priority] || 0) + count
    })

    // 统计批量任务
    this.batchQueue.forEach((queue, priority) => {
      const count = queue.length
      totalTasks += count
      tasksByPriority[priority] = (tasksByPriority[priority] || 0) + count
    })

    return {
      totalTasks,
      runningTasks: this.runningTasks.size,
      tasksByPriority,
      averageFrameTime: this.averageFrameTime,
      currentTimeSlice: this.config.maxExecutionTime
    }
  }

  /**
   * 获取性能分析数据
   */
  public getProfilingData(): ProfilingData[] {
    return [...this.profilingData]
  }

  /**
   * 清除性能分析数据
   */
  public clearProfilingData(): void {
    this.profilingData.length = 0
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 暂停调度器
   */
  public pause(): void {
    this.isRunning = false
    if (this.frameId) {
      cancelAnimationFrame(this.frameId)
      this.frameId = undefined
    }
  }

  /**
   * 恢复调度器
   */
  public resume(): void {
    if (!this.isRunning) {
      this.startScheduler()
    }
  }

  /**
   * 销毁调度器
   */
  public destroy(): void {
    this.pause()
    this.clearAllTasks()
    this.clearProfilingData()
  }
}

// 单例实例
let schedulerInstance: TimeSliceScheduler | null = null

/**
 * 获取时间切片调度器单例
 */
export function getTimeSliceScheduler(): TimeSliceScheduler {
  if (!schedulerInstance) {
    schedulerInstance = new TimeSliceScheduler()
  }
  return schedulerInstance
}

/**
 * 销毁时间切片调度器单例
 */
export function destroyTimeSliceScheduler(): void {
  if (schedulerInstance) {
    schedulerInstance.destroy()
    schedulerInstance = null
  }
}

/**
 * 便捷函数：执行时间切片任务
 */
export function executeWithTimeSlicing<T>(
  task: () => T | Promise<T>,
  priority: TaskPriority = TaskPriority.NORMAL
): Promise<T> {
  return new Promise((resolve, reject) => {
    const scheduler = getTimeSliceScheduler()
    scheduler.addTask(task, {
      priority,
      onComplete: resolve,
      onError: reject
    })
  })
}

/**
 * 便捷函数：批量处理数据
 */
export function processBatchWithTimeSlicing<T, R>(
  items: T[],
  processor: (item: T, index: number) => R | Promise<R>,
  options: {
    priority?: TaskPriority
    batchSize?: number
    onProgress?: (completed: number, total: number) => void
  } = {}
): Promise<R[]> {
  return new Promise((resolve, reject) => {
    const scheduler = getTimeSliceScheduler()
    scheduler.addBatchTask(items, processor, {
      priority: options.priority,
      batchSize: options.batchSize,
      onProgress: options.onProgress ? 
        (completed, total, results) => options.onProgress!(completed, total) : 
        undefined,
      onComplete: resolve,
      onError: reject
    })
  })
}