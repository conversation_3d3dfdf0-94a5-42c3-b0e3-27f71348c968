// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  role: 'admin' | 'user'
  created_at: string
  updated_at: string
}

// 用户角色类型
export type UserRole = 'admin' | 'user'

// 管理员用户管理相关类型
export interface UserManagement {
  id: number
  username: string
  email: string
  role: UserR<PERSON>
  created_at: string
  updated_at: string
  last_login?: string
  is_active: boolean
}

// 更新用户角色请求
export interface UpdateUserRoleRequest {
  user_id: number
  role: UserRole
}

// 用户资料更新请求
export interface UpdateProfileRequest {
  email: string
  username?: string
}

// 密码修改请求
export interface ChangePasswordRequest {
  old_password: string
  new_password: string
  confirm_password: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

// 文件相关类型
export interface FileItem {
  id: number
  name: string
  original_name: string
  path: string
  size: number
  is_directory: boolean
  modified_time: string
  created_at: string
  updated_at: string
}

// 重命名相关类型
export interface RenameRequest {
  file_ids: number[]
  operation_type: 'regex' | 'sequence' | 'prefix_suffix' | 'case_conversion'
  parameters: {
    pattern?: string
    replacement?: string
    start_number?: number
    step?: number
    prefix?: string
    suffix?: string
    case_type?: 'upper' | 'lower' | 'title' | 'camel' | 'snake'
  }
}

// 重命名表单类型（用于前端表单绑定）
export interface RenameForm {
  file_ids: number[]
  type: 'regex' | 'sequence' | 'prefix' | 'suffix' | 'case'
  pattern?: string
  replacement?: string
  start_number?: number
  prefix?: string
  suffix?: string
  case_type?: 'upper' | 'lower' | 'title' | 'camel' | 'snake'
}

export interface RenamePreview {
  file_id: number
  original_name: string
  new_name: string
  success: boolean
  valid: boolean
  error?: string
}

// 操作日志类型
export interface OperationLog {
  id: number
  user_id: number
  operation_type: string
  file_path: string
  old_name: string
  new_name: string
  success: boolean
  error_message?: string
  created_at: string
}

// 统计信息类型
export interface Stats {
  total_files: number
  file_count: number
  directory_count: number
  total_size: number
  storage_used: number
  total_operations: number
  successful_operations: number
  failed_operations: number
}

// 目录类型
export interface Directory {
  id: number
  name: string
  path: string
  parent_id?: number
  user_id: number
  created_at: string
  updated_at: string
}

// 上传文件类型
export interface UploadFile {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}