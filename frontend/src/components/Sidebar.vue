<template>
  <div class="sidebar" :class="{ collapsed }">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="logo">
        <div class="logo-icon">
          <el-icon><Files /></el-icon>
        </div>
        <transition name="fade">
          <span v-show="!collapsed" class="logo-text">文件管理工具</span>
        </transition>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav" role="navigation" aria-label="主导航">
      <div class="nav-section">
        <div class="section-title" v-if="!collapsed">主要功能</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in mainMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{
              active: currentActiveItem === item.path
            }"
            @click="handleNavClick"
            @mouseleave="handleMouseLeave"
            :aria-label="item.title"
            :title="collapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
            <transition name="fade">
              <div v-show="!collapsed && item.badge" class="nav-badge">
                {{ item.badge }}
              </div>
            </transition>
          </router-link>
        </div>
      </div>

      <div class="nav-section">
        <div class="section-title" v-if="!collapsed">系统管理</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in systemMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{
              active: currentActiveItem === item.path
            }"
            @click="handleNavClick"
            @mouseleave="handleMouseLeave"
            :aria-label="item.title"
            :title="collapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 展开状态的按钮 -->
      <div
        v-if="!collapsed"
        class="collapse-btn-expanded"
        @click="handleToggle"
        role="button"
        aria-label="收起侧边栏"
        tabindex="0"
        @keydown.enter="handleToggle"
      >
        <el-icon>
          <Fold />
        </el-icon>
      </div>
      <!-- 收缩状态的按钮 -->
      <div
        v-else
        class="collapse-btn-collapsed"
        @click="handleToggle"
        role="button"
        aria-label="展开侧边栏"
        tabindex="0"
        @keydown.enter="handleToggle"
      >
        <el-icon>
          <Expand />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onUnmounted, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import {
  Files,
  DataBoard,
  Upload,
  Edit,
  List,
  Setting,
  Expand,
  Fold
} from '@element-plus/icons-vue'

interface Props {
  collapsed: boolean
}

defineProps<Props>()
const emit = defineEmits<{
  toggle: []
}>()

const route = useRoute()

// 简化状态管理 - 直接使用路由状态，确保同步
const currentActiveItem = computed(() => {
  console.log('当前路由路径:', route.path)
  return route.path
})

const handleToggle = () => {
  emit('toggle')
}

// 简化导航点击处理将在下面重新定义

// 智能清理函数 - 只清理不需要的样式，保留active状态
const clearNavItemStyles = () => {
  const allNavItems = document.querySelectorAll('.nav-item')
  allNavItems.forEach(item => {
    const element = item as HTMLElement
    
    // 只清理可能造成残留的transform和box-shadow
    if (element.style.transform && !element.style.transform.includes('translateZ')) {
      element.style.transform = ''
    }
    if (element.style.boxShadow) {
      element.style.boxShadow = ''
    }
    if (element.style.background && !element.classList.contains('active')) {
      element.style.background = ''
    }
    
    // 清理子元素的transform
    const icon = element.querySelector('.nav-icon') as HTMLElement
    const elIcon = element.querySelector('.el-icon') as HTMLElement
    
    if (icon && icon.style.transform) {
      icon.style.transform = ''
    }
    if (elIcon && elIcon.style.transform) {
      elIcon.style.transform = ''
    }
    
    // 只移除非必要的类，保留active
    element.classList.remove('hover', 'focus', 'nav-item-switching')
  })
}

// 鼠标离开时立即清理
const handleMouseLeave = (event: MouseEvent) => {
  const target = event.currentTarget as HTMLElement
  if (target) {
    // 延迟清理，确保CSS动画完成
    setTimeout(() => {
      target.removeAttribute('style')
      target.style.cssText = ''
      
      // 清理子元素
      const icon = target.querySelector('.nav-icon')
      const elIcon = target.querySelector('.el-icon')
      if (icon) (icon as HTMLElement).removeAttribute('style')
      if (elIcon) (elIcon as HTMLElement).removeAttribute('style')
    }, 50)
  }
}

// 点击时清理悬停残留，但保留active状态
const handleNavClick = () => {
  clearNavItemStyles()
}

// 轻量级定期清理机制
let cleanupInterval: NodeJS.Timeout | null = null

onMounted(() => {
  // 每2秒清理一次悬停残留，但不影响active状态
  cleanupInterval = setInterval(() => {
    clearNavItemStyles()
  }, 2000)
})

onUnmounted(() => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval)
  }
})

// 路由变化时智能清理 - 保留active状态
watch(() => route.path, () => {
  // 使用智能清理函数
  clearNavItemStyles()
  
  nextTick(() => {
    clearNavItemStyles()
  })
}, { immediate: true })

// 主要功能菜单项
const mainMenuItems = computed(() => {
  const items = [
    {
      path: '/dashboard',
      title: '仪表板',
      icon: 'DataBoard',
      badge: null
    },
    {
      path: '/files',
      title: '文件管理',
      icon: 'Upload',
      badge: null
    },
    {
      path: '/rename',
      title: '批量重命名',
      icon: 'Edit',
      badge: null
    }
  ]
  console.log('菜单项:', items)
  console.log('当前活跃项:', currentActiveItem.value)
  return items
})

// 系统管理菜单项
const systemMenuItems = computed(() => [
  {
    path: '/logs',
    title: '操作日志',
    icon: 'List',
    badge: null
  },
  {
    path: '/profile',
    title: '个人设置',
    icon: 'Setting',
    badge: null
  }
])

</script>

<style scoped>
/* 简化的动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-10px);
  }
}

.sidebar {
  width: 260px;
  background: var(--bg-card);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  /* 简化过渡动画 */
  transition: width 0.2s ease;
  position: relative;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  min-width: 260px;
}

.sidebar.collapsed {
  width: 64px;
  min-width: 64px;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  transition: padding 0.2s ease;
  overflow: hidden;
}

.sidebar.collapsed .sidebar-header {
  padding: var(--space-4) 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: var(--primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
}

/* 导航菜单 */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 收缩状态下的导航区域优化 */
.sidebar.collapsed .sidebar-nav {
  padding: var(--space-4) var(--space-3);
  display: block; /* 改为block，避免flex布局问题 */
}

.nav-section {
  margin-bottom: var(--space-6);
  overflow: hidden;
}

.section-title {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 var(--space-4);
  margin-bottom: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
  text-align: left;
  transition: opacity 0.2s ease;
  position: relative;
}

/* 收缩状态下隐藏分类标题 */
.sidebar.collapsed .nav-section {
  margin-bottom: var(--space-3);
}

.sidebar.collapsed .section-title {
  display: none;
}

/* 导航项容器优化 */
.nav-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  padding: 0 var(--space-2);
  overflow: hidden;
}

.sidebar.collapsed .nav-items {
  padding: 0;
  gap: var(--space-2);
  align-items: center;
}

/* 移除复杂的进入动画 */

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  /* 简化过渡动画 */
  transition: all 0.15s ease;
  position: relative;
  min-height: 44px;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}

/* 简化按压效果 */
.nav-item:active {
  background: var(--bg-tertiary);
  transition: background-color 0.05s ease;
}

/* 收缩状态下的导航项统一尺寸 - 优化悬停性能 */
.sidebar.collapsed .nav-item {
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0 auto var(--space-2) auto; /* 水平居中，统一垂直间距 */
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  /* 优化的过渡属性，分离transform避免卡顿 */
  transition: background-color 0.15s ease, 
              color 0.15s ease, 
              box-shadow 0.15s ease,
              transform 0.12s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0); /* 启用硬件加速但不强制GPU层 */
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  display: flex; /* 确保flex布局 */
}

/* 收缩状态下的按压效果 */
.sidebar.collapsed .nav-item:active {
  transform: scale(0.95);
}

/* 收缩状态下隐藏文字 - 使用visibility避免动画冲突 */
.sidebar.collapsed .nav-text {
  visibility: hidden;
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-item:hover .nav-icon {
  color: var(--primary);
}

/* 收缩状态下的悬停效果 - 优化性能 */
.sidebar.collapsed .nav-item:hover:not(.active) {
  background: var(--bg-hover);
  /* 使用更轻量的transform，避免模糊 */
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

.sidebar.collapsed .nav-item:hover:not(.active) .nav-icon {
  color: var(--primary);
}

/* 展开状态下的active样式 - 确保正确显示 */
.sidebar:not(.collapsed) .nav-item.active {
  background: rgba(99, 102, 241, 0.1) !important;
  color: var(--primary) !important;
  font-weight: 600 !important;
  border-left: 3px solid var(--primary) !important;
  padding-left: calc(var(--space-4) - 3px) !important;
  transition: all 0.15s ease;
}

.sidebar:not(.collapsed) .nav-item.active .nav-icon {
  color: var(--primary) !important;
}

/* 收缩状态下的active样式 - 确保正确显示 */
.sidebar.collapsed .nav-item.active {
  background: var(--primary) !important;
  color: white !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 12px !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 0 !important;
  margin: 0 auto var(--space-2) auto !important;
  display: flex !important;
  transition: background-color 0.15s ease, color 0.15s ease;
}

.sidebar.collapsed .nav-item.active .nav-icon {
  color: white !important;
}

.sidebar.collapsed .nav-item.active:hover {
  background: var(--primary-hover) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 图标容器基础样式 */
.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
  transition: color 0.15s ease;
  position: relative;
  /* 确保容器不被压缩 */
  min-width: 24px;
  min-height: 24px;
}

/* Element Plus图标组件样式重置 - 确保显示 */
.nav-icon .el-icon {
  width: 20px !important;
  height: 20px !important;
  font-size: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  line-height: 1 !important;
  color: inherit !important;
  /* 确保图标可见 */
  opacity: 1 !important;
  visibility: visible !important;
}

/* 图标内的SVG样式 - 强制显示 */
.nav-icon .el-icon svg {
  width: 18px !important;
  height: 18px !important;
  fill: currentColor !important;
  stroke: currentColor !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* 确保图标路径显示 */
.nav-icon .el-icon svg path {
  fill: currentColor !important;
  stroke: currentColor !important;
  opacity: 1 !important;
}

/* 收缩状态下的图标 */
.sidebar.collapsed .nav-icon {
  width: 24px;
  height: 24px;
  font-size: 18px;
  /* 确保在收缩状态下图标完全可见 */
  margin: 0;
  padding: 0;
}

.nav-text {
  flex: 1;
  font-size: var(--text-sm);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  transition: opacity 0.2s ease;
}

.nav-badge {
  background: var(--danger);
  color: white;
  font-size: var(--text-xs);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 72px;
  transition: padding 0.2s ease;
}

/* 展开状态的按钮 */
.collapse-btn-expanded {
  position: absolute;
  width: calc(100% - var(--space-4) * 2);
  height: 40px;
  background: var(--bg-hover);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.collapse-btn-expanded:active {
  transform: scale(0.95);
}

/* 收缩状态的按钮 */
.collapse-btn-collapsed {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  border: none;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
  transform: scale(0.8);
}

.collapse-btn-collapsed:active {
  transform: scale(0.9);
}


/* 收缩状态 */
.sidebar.collapsed .sidebar-footer {
  padding: var(--space-2);
  border-top: none;
  height: 52px;
}

.sidebar.collapsed .collapse-btn-expanded {
  opacity: 0;
  transform: scale(0.8);
}

.sidebar.collapsed .collapse-btn-collapsed {
  opacity: 1;
  transform: scale(1);
}

/* 悬停效果 */
.collapse-btn-expanded:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.collapse-btn-collapsed:hover {
  background: var(--primary-hover);
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 6px 24px rgba(99, 102, 241, 0.5);
}

/* 简化fade动画 */
.fade-enter-active {
  transition: all 0.2s ease;
  animation: fadeIn 0.2s ease;
}

.fade-leave-active {
  transition: all 0.2s ease;
  animation: fadeOut 0.2s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateX(-10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}


/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1001;
    /* 移动端不显示收缩状态，直接隐藏 */
  }
  
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
  
  /* 移动端导航项优化 */
  .nav-item {
    min-height: 48px;
    padding: var(--space-3) var(--space-4);
  }
  
  /* 移动端收缩状态优化 */
  .sidebar.collapsed .nav-item {
    width: 44px;
    height: 44px;
    margin: 0 auto var(--space-3) auto;
  }
  
  /* 移动端收缩状态active样式 */
  .sidebar.collapsed .nav-item.active {
    width: 44px;
    height: 44px;
    margin: 0 auto var(--space-3) auto;
  }
}

/* 触摸设备优化 - 避免卡顿 */
@media (hover: none) and (pointer: coarse) {
  /* 禁用悬停效果的transform，避免触摸设备上的问题 */
  .nav-item:hover {
    transform: translateZ(0); /* 保持基础硬件加速 */
  }
  
  .sidebar.collapsed .nav-item:hover:not(.active) {
    transform: translateZ(0);
    /* 保留背景色变化，移除transform动画 */
    box-shadow: none;
  }
  
  .sidebar.collapsed .nav-item.active:hover {
    transform: translateZ(0);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
  }
  
  /* 触摸设备上的快速按压反馈 */
  .nav-item:active {
    background: var(--bg-tertiary);
    transform: scale(0.98) translateZ(0);
    transition: transform 0.08s ease-out; /* 更快的反馈 */
  }
  
  .sidebar.collapsed .nav-item:active {
    transform: scale(0.95) translateZ(0);
    transition: transform 0.08s ease-out;
  }
}

/* 优化的基础样式 - 移除可能导致卡顿的属性 */
.nav-item {
  /* 确保所有导航项都有相同的基础样式 */
  box-sizing: border-box;
  outline: none;
  -webkit-tap-highlight-color: transparent;
  /* 防止文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 移除will-change，避免不必要的GPU层 */
}

/* 确保图标在任何状态下都居中 */
.nav-icon {
  /* 防止图标在某些状态下错位 */
  position: relative;
  z-index: 1;
}

/* 确保收缩状态下图标完整显示 */
.sidebar.collapsed .nav-item {
  /* 防止内容溢出 */
  box-sizing: border-box;
  /* 确保图标在中心且不被裁切 */
  min-width: 40px;
  min-height: 40px;
  /* 强制重置可能的残留样式 */
  border: none !important;
  background-clip: padding-box;
}

/* 防止快速切换时的闪烁 */
.sidebar.collapsed .nav-text,
.sidebar.collapsed .nav-badge {
  /* 确保在收缩状态下完全不可见 */
  position: absolute;
  left: -9999px;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* 轻量级的侧边栏优化 */
.sidebar {
  /* 仅保留必要的优化 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 强制重绘类 - 彻底清理残留 */
.sidebar.force-repaint {
  transform: translateZ(0.1px);
}

.sidebar.force-repaint .nav-item {
  opacity: 0.999; /* 强制重新渲染 */
}

/* 收缩状态下的图标确保完整显示 */
.sidebar.collapsed .nav-icon {
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: inherit !important;
  position: relative !important;
  z-index: 1 !important;
}

.sidebar.collapsed .nav-icon .el-icon {
  width: 20px !important;
  height: 20px !important;
  font-size: 18px !important;
}

/* 防止任何残留和伪元素 */
.nav-item::before,
.nav-item::after {
  display: none !important;
  content: none !important;
}

/* 清理重复定义完成 */
</style>
