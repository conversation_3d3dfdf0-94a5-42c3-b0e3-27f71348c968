<template>
  <header class="app-header">
    <div class="header-left">
      <!-- 侧边栏切换按钮 -->
      <button class="sidebar-toggle" @click="$emit('toggleSidebar')">
        <el-icon>
          <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
        </el-icon>
      </button>
      
      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentPageTitle">{{ currentPageTitle }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <div class="header-right">
      <!-- 通知按钮 -->
      <div class="header-action">
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <button class="action-btn">
            <el-icon><Bell /></el-icon>
          </button>
        </el-badge>
      </div>

      <!-- 用户菜单 -->
      <el-dropdown class="user-dropdown" @command="handleUserCommand">
        <div class="user-info">
          <div class="user-avatar">
            <el-icon><User /></el-icon>
          </div>
          <div class="user-details" v-show="!sidebarCollapsed">
            <div class="user-name">{{ user?.username || '用户' }}</div>
            <div class="user-role">管理员</div>
          </div>
          <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人资料
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import {
  Bell,
  User,
  ArrowDown,
  SwitchButton,
  Setting,
  Expand,
  Fold
} from '@element-plus/icons-vue'

interface Props {
  sidebarCollapsed: boolean
}

defineProps<Props>()
defineEmits<{
  toggleSidebar: []
}>()

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const user = computed(() => authStore.user)
const notificationCount = computed(() => 0) // 暂时设为0，后续可以添加通知功能

// 当前页面标题
const currentPageTitle = computed(() => {
  const titleMap: Record<string, string> = {
    '/dashboard': '',
    '/files': '文件管理',
    '/rename': '批量重命名',
    '/logs': '操作日志',
    '/profile': '个人设置'
  }
  return titleMap[route.path] || ''
})

// 处理用户菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/profile')
      break
    case 'logout':
      try {
        await authStore.logout()
        ElMessage.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        ElMessage.error('退出登录失败')
      }
      break
  }
}
</script>

<style scoped>
.app-header {
  height: 64px;
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-6);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 998;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  height: 100%;
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  background: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.sidebar-toggle:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--primary);
}

.breadcrumb {
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  height: 40px;
  margin-top: 9px;
}

.breadcrumb :deep(.el-breadcrumb) {
  display: flex !important;
  align-items: center !important;
  height: 40px !important;
  line-height: 1 !important;
}

.breadcrumb :deep(.el-breadcrumb__item) {
  display: flex !important;
  align-items: center !important;
  height: 40px !important;
  line-height: 1 !important;
}

.breadcrumb :deep(.el-breadcrumb__inner) {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
  padding: 0 !important;
  transform: translateY(5px) !important;
}

.breadcrumb :deep(.el-breadcrumb__inner.is-link) {
  color: var(--primary);
}

.breadcrumb :deep(.el-breadcrumb__inner.is-link:hover) {
  color: var(--primary-hover);
}

.breadcrumb :deep(.el-breadcrumb__separator) {
  color: var(--text-tertiary) !important;
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  transform: translateY(5px) !important;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.header-action {
  position: relative;
}

.action-btn {
  width: 40px;
  height: 40px;
  background: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--primary);
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.user-info:hover {
  background: var(--bg-hover);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: var(--primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.user-role {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  line-height: 1.2;
}

.dropdown-arrow {
  color: var(--text-tertiary);
  font-size: var(--text-sm);
  transition: transform var(--transition-normal);
}

.user-dropdown.is-active .dropdown-arrow {
  transform: rotate(180deg);
}

/* Element Plus 下拉菜单样式重写 */
:deep(.el-dropdown-menu) {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-xl);
  padding: var(--space-2);
}

:deep(.el-dropdown-menu__item) {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: all var(--transition-normal);
}

:deep(.el-dropdown-menu__item:hover) {
  background: var(--bg-hover);
  color: var(--text-primary);
}

:deep(.el-dropdown-menu__item.is-divided) {
  border-top: 1px solid var(--border-primary);
  margin-top: var(--space-2);
  padding-top: var(--space-3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 var(--space-4);
  }
  
  .user-details {
    display: none;
  }
  
  .breadcrumb {
    display: none;
  }
}
</style>