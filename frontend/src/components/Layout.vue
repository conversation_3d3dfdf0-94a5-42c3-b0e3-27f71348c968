<template>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <Sidebar 
      :collapsed="sidebarCollapsed" 
      @toggle="toggleSidebar"
      class="app-sidebar"
    />
    
    <!-- 主内容区域 -->
    <div class="app-main" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- 顶部栏 -->
      <Header 
        :sidebar-collapsed="sidebarCollapsed"
        @toggle-sidebar="toggleSidebar"
        class="app-header"
      />
      
      <!-- 页面内容 -->
      <main class="app-content">
        <div class="content-container">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Sidebar from './Sidebar.vue'
import Header from './Header.vue'

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 切换侧边栏状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存状态到localStorage
  localStorage.setItem('sidebar-collapsed', String(sidebarCollapsed.value))
}

// 从localStorage恢复侧边栏状态
onMounted(() => {
  const saved = localStorage.getItem('sidebar-collapsed')
  if (saved !== null) {
    sidebarCollapsed.value = saved === 'true'
  }
})
</script>

<style scoped>
.app-layout {
  display: flex;
  height: 100vh;
  background: var(--bg-primary);
}

.app-sidebar {
  flex-shrink: 0;
  z-index: 1000;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  transition: margin-left var(--transition-normal);
}

.app-header {
  flex-shrink: 0;
  z-index: 999;
}

.app-content {
  flex: 1;
  overflow: hidden;
  background: var(--bg-primary);
}

.content-container {
  height: 100%;
  overflow-y: auto;
  padding: var(--space-6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: var(--space-4);
  }
}

/* 滚动条样式 */
.content-container::-webkit-scrollbar {
  width: 6px;
}

.content-container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.content-container::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}
</style>