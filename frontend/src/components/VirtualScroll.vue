<template>
  <div 
    ref="containerRef" 
    class="virtual-scroll-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <!-- 虚拟滚动内容区域 -->
    <div 
      class="virtual-scroll-content"
      :style="{ 
        height: totalHeight + 'px',
        transform: `translateY(${offsetY}px)`
      }"
    >
      <!-- 渲染的可见项目 -->
      <div
        v-for="(item, index) in visibleItems"
        :key="getItemKey(item, startIndex + index)"
        :ref="el => setItemRef(el as HTMLElement | null, startIndex + index)"
        class="virtual-scroll-item"
        :style="getItemStyle(startIndex + index)"
        :data-index="startIndex + index"
      >
        <slot 
          :item="item" 
          :index="startIndex + index"
          :isVisible="true"
        />
      </div>
    </div>

    <!-- 滚动指示器 -->
    <div 
      v-if="showScrollIndicator && isScrolling" 
      class="scroll-indicator"
    >
      {{ Math.round(scrollProgress * 100) }}%
    </div>

    <!-- 性能监控面板 -->
    <div 
      v-if="showPerformanceMonitor && performanceData" 
      class="performance-monitor"
    >
      <div class="perf-item">FPS: {{ performanceData.fps }}</div>
      <div class="perf-item">渲染时间: {{ performanceData.renderTime }}ms</div>
      <div class="perf-item">可见项: {{ visibleItems.length }}</div>
      <div class="perf-item">总项数: {{ items.length }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

// 组件属性
interface Props {
  items: any[]
  itemHeight: number | ((index: number) => number)
  containerHeight: number
  overscan?: number
  keyField?: string
  showScrollIndicator?: boolean
  showPerformanceMonitor?: boolean
  enableSmoothScrolling?: boolean
  recycleThreshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  overscan: 5,
  keyField: 'id',
  showScrollIndicator: false,
  showPerformanceMonitor: false,
  enableSmoothScrolling: true,
  recycleThreshold: 100
})

// 组件事件
const emit = defineEmits<{
  scroll: [scrollTop: number, scrollDirection: 'up' | 'down']
  visibleRangeChange: [startIndex: number, endIndex: number]
  itemsRendered: [count: number]
}>()

// 响应式引用
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const isScrolling = ref(false)
const scrollTimer = ref<number>()
const lastScrollTop = ref(0)
const itemRefs = ref<Map<number, HTMLElement>>(new Map())

// DOM元素池用于复用
const elementPool = ref<HTMLElement[]>([])
const activeElements = ref<Map<number, HTMLElement>>(new Map())

// 性能监控
const performanceData = ref<{
  fps: number
  renderTime: number
  lastFrameTime: number
  frameCount: number
}>({
  fps: 60,
  renderTime: 0,
  lastFrameTime: performance.now(),
  frameCount: 0
})

// 滚动性能优化
let rafId: number | null = null
let lastScrollTime = 0
const scrollThrottle = 16 // 约60fps

// 计算属性
const totalHeight = computed(() => {
  if (typeof props.itemHeight === 'number') {
    return props.items.length * props.itemHeight
  }
  
  // 动态高度计算
  let height = 0
  for (let i = 0; i < props.items.length; i++) {
    height += props.itemHeight(i)
  }
  return height
})

const visibleRange = computed(() => {
  const containerH = props.containerHeight
  const overscan = props.overscan
  
  if (typeof props.itemHeight === 'number') {
    // 固定高度计算
    const itemH = props.itemHeight
    const startIndex = Math.max(0, Math.floor(scrollTop.value / itemH) - overscan)
    const visibleCount = Math.ceil(containerH / itemH) + overscan * 2
    const endIndex = Math.min(props.items.length - 1, startIndex + visibleCount)
    
    return { startIndex, endIndex }
  } else {
    // 动态高度计算
    let accumulatedHeight = 0
    let startIndex = 0
    let endIndex = 0
    
    // 找到开始索引
    for (let i = 0; i < props.items.length; i++) {
      const itemHeight = props.itemHeight(i)
      if (accumulatedHeight + itemHeight > scrollTop.value) {
        startIndex = Math.max(0, i - overscan)
        break
      }
      accumulatedHeight += itemHeight
    }
    
    // 找到结束索引
    accumulatedHeight = 0
    for (let i = startIndex; i < props.items.length; i++) {
      const itemHeight = props.itemHeight(i)
      accumulatedHeight += itemHeight
      if (accumulatedHeight > containerH + overscan * (props.itemHeight as any)(0)) {
        endIndex = Math.min(props.items.length - 1, i + overscan)
        break
      }
    }
    
    if (endIndex === 0) endIndex = props.items.length - 1
    
    return { startIndex, endIndex }
  }
})

const startIndex = computed(() => visibleRange.value.startIndex)
const endIndex = computed(() => visibleRange.value.endIndex)

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1)
})

const offsetY = computed(() => {
  if (typeof props.itemHeight === 'number') {
    return startIndex.value * props.itemHeight
  }
  
  // 动态高度偏移计算
  let offset = 0
  for (let i = 0; i < startIndex.value; i++) {
    offset += props.itemHeight(i)
  }
  return offset
})

const scrollProgress = computed(() => {
  const maxScroll = totalHeight.value - props.containerHeight
  return maxScroll > 0 ? scrollTop.value / maxScroll : 0
})

// 方法
const getItemKey = (item: any, index: number): string | number => {
  if (props.keyField && item[props.keyField] !== undefined) {
    return item[props.keyField]
  }
  return index
}

const getItemStyle = (index: number) => {
  if (typeof props.itemHeight === 'number') {
    return {
      height: props.itemHeight + 'px',
      position: 'absolute' as const,
      top: (index - startIndex.value) * props.itemHeight + 'px',
      left: '0',
      right: '0'
    }
  }
  
  // 动态高度样式
  let top = 0
  for (let i = startIndex.value; i < index; i++) {
    top += props.itemHeight(i)
  }
  
  return {
    height: props.itemHeight(index) + 'px',
    position: 'absolute' as const,
    top: top + 'px',
    left: '0',
    right: '0'
  }
}

const setItemRef = (el: HTMLElement | null, index: number) => {
  if (el) {
    itemRefs.value.set(index, el)
    activeElements.value.set(index, el)
  } else {
    const element = itemRefs.value.get(index)
    if (element) {
      itemRefs.value.delete(index)
      activeElements.value.delete(index)
      
      // 回收到元素池
      if (elementPool.value.length < props.recycleThreshold) {
        elementPool.value.push(element)
      }
    }
  }
}

const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  const newScrollTop = target.scrollTop
  
  // 节流处理
  const now = performance.now()
  if (now - lastScrollTime < scrollThrottle) {
    return
  }
  lastScrollTime = now

  // 取消之前的动画帧
  if (rafId) {
    cancelAnimationFrame(rafId)
  }

  rafId = requestAnimationFrame(() => {
    const startTime = performance.now()
    
    scrollTop.value = newScrollTop
    isScrolling.value = true
    
    // 确定滚动方向
    const direction = newScrollTop > lastScrollTop.value ? 'down' : 'up'
    lastScrollTop.value = newScrollTop
    
    // 发出滚动事件
    emit('scroll', newScrollTop, direction)
    
    // 清除滚动状态定时器
    if (scrollTimer.value) {
      clearTimeout(scrollTimer.value)
    }
    
    scrollTimer.value = window.setTimeout(() => {
      isScrolling.value = false
    }, 150)
    
    // 更新性能数据
    const endTime = performance.now()
    performanceData.value.renderTime = endTime - startTime
    updateFPS()
    
    rafId = null
  })
}

const updateFPS = () => {
  const now = performance.now()
  const delta = now - performanceData.value.lastFrameTime
  
  if (delta >= 1000) {
    performanceData.value.fps = Math.round(
      (performanceData.value.frameCount * 1000) / delta
    )
    performanceData.value.frameCount = 0
    performanceData.value.lastFrameTime = now
  } else {
    performanceData.value.frameCount++
  }
}

const scrollToIndex = (index: number, behavior: ScrollBehavior = 'smooth') => {
  if (!containerRef.value) return
  
  let targetScrollTop = 0
  
  if (typeof props.itemHeight === 'number') {
    targetScrollTop = index * props.itemHeight
  } else {
    for (let i = 0; i < index; i++) {
      targetScrollTop += props.itemHeight(i)
    }
  }
  
  containerRef.value.scrollTo({
    top: targetScrollTop,
    behavior: props.enableSmoothScrolling ? behavior : 'auto'
  })
}

const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
  scrollToIndex(0, behavior)
}

const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
  if (!containerRef.value) return
  
  containerRef.value.scrollTo({
    top: totalHeight.value,
    behavior: props.enableSmoothScrolling ? behavior : 'auto'
  })
}

const getVisibleRange = () => {
  return {
    startIndex: startIndex.value,
    endIndex: endIndex.value,
    visibleCount: visibleItems.value.length
  }
}

const forceUpdate = () => {
  nextTick(() => {
    if (containerRef.value) {
      const mockEvent = {
        target: containerRef.value
      } as unknown as Event
      handleScroll(mockEvent)
    }
  })
}

// 监听可见范围变化
watch(
  () => [startIndex.value, endIndex.value],
  ([newStart, newEnd], [oldStart, oldEnd]) => {
    if (newStart !== oldStart || newEnd !== oldEnd) {
      emit('visibleRangeChange', newStart, newEnd)
      emit('itemsRendered', newEnd - newStart + 1)
    }
  }
)

// 监听项目数量变化
watch(
  () => props.items.length,
  () => {
    nextTick(() => {
      forceUpdate()
    })
  }
)

// 生命周期
onMounted(() => {
  if (containerRef.value) {
    // 初始化滚动位置
    nextTick(() => {
      forceUpdate()
    })
  }
})

onUnmounted(() => {
  if (rafId) {
    cancelAnimationFrame(rafId)
  }
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
  }
  
  // 清理元素池
  elementPool.value.length = 0
  activeElements.value.clear()
  itemRefs.value.clear()
})

// 暴露方法
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
  getVisibleRange,
  forceUpdate,
  containerRef
})
</script>

<style scoped>
.virtual-scroll-container {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  will-change: scroll-position;
}

.virtual-scroll-content {
  position: relative;
  width: 100%;
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
}

.virtual-scroll-item {
  width: 100%;
  /* 优化渲染性能 */
  contain: layout style paint;
  transform: translateZ(0);
}

/* 滚动指示器 */
.scroll-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
  pointer-events: none;
  backdrop-filter: blur(4px);
}

/* 性能监控面板 */
.performance-monitor {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #00ff00;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  z-index: 10;
  pointer-events: none;
  backdrop-filter: blur(4px);
}

.perf-item {
  margin-bottom: 2px;
}

.perf-item:last-child {
  margin-bottom: 0;
}

/* 自定义滚动条 */
.virtual-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Firefox 滚动条 */
.virtual-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .virtual-scroll-container {
    scroll-behavior: auto !important;
  }
  
  .scroll-indicator,
  .performance-monitor {
    transition: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .scroll-indicator {
    background: #000;
    color: #fff;
    border: 1px solid #fff;
  }
  
  .performance-monitor {
    background: #000;
    color: #0f0;
    border: 1px solid #0f0;
  }
}
</style>