/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/tree/main/packages/reactivity-transform#refs-vs-reactive
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElButton: typeof import('element-plus')['ElButton']
    ElCard: typeof import('element-plus')['ElCard']
    ElCol: typeof import('element-plus')['ElCol']
    ElDialog: typeof import('element-plus')['ElDialog']
    ElForm: typeof import('element-plus')['ElForm']
    ElFormItem: typeof import('element-plus')['ElFormItem']
    ElIcon: typeof import('element-plus')['ElIcon']
    ElInput: typeof import('element-plus')['ElInput']
    ElInputNumber: typeof import('element-plus')['ElInputNumber']
    ElMessage: typeof import('element-plus')['ElMessage']
    ElMessageBox: typeof import('element-plus')['ElMessageBox']
    ElOption: typeof import('element-plus')['ElOption']
    ElPagination: typeof import('element-plus')['ElPagination']
    ElProgress: typeof import('element-plus')['ElProgress']
    ElRow: typeof import('element-plus')['ElRow']
    ElSelect: typeof import('element-plus')['ElSelect']
    ElTable: typeof import('element-plus')['ElTable']
    ElTableColumn: typeof import('element-plus')['ElTableColumn']
    ElTag: typeof import('element-plus')['ElTag']
    ElTooltip: typeof import('element-plus')['ElTooltip']
    ElUpload: typeof import('element-plus')['ElUpload']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}