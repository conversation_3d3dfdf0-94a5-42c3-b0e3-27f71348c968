/**
 * 重命名计算 Web Worker
 * 用于在后台线程处理大量文件的重命名计算，避免阻塞主线程
 */

// 文件项接口定义
interface FileItem {
  id: number
  original_name: string
  size: number
  created_at: string
}

// 重命名配置接口
interface RenameConfig {
  type: 'regex' | 'prefix' | 'suffix' | 'replace' | 'mapping' | 'sequence' | 'extension'
  regexPattern?: string
  regexReplacement?: string
  prefixText?: string
  suffixText?: string
  findText?: string
  replaceText?: string
  baseName?: string
  startNumber?: number
  numberPadding?: number
  newExtension?: string
  mappingFrom?: string
  mappingTo?: string
}

// 预览结果接口
interface PreviewResult {
  original: string
  new: string
  hasChanges: boolean
  changeType: 'prefix' | 'suffix' | 'replace' | 'extension' | 'sequence' | 'none'
  index: number
}

// 计算任务接口
interface ComputeTask {
  id: string
  files: FileItem[]
  config: RenameConfig
  batchSize?: number
}

// 工具函数：获取文件扩展名
const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

// 工具函数：获取不含扩展名的文件名
const getFileNameWithoutExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(0, lastDot) : filename
}

// 解析映射列表
const parseMappingList = (text: string): string[] => {
  if (!text || !text.trim()) {
    return []
  }
  
  return text.trim()
    .split(/[\n\s,]+/)
    .map(item => item.trim())
    .filter(item => item.length > 0)
}

// 获取变更类型
const getChangeType = (original: string, newName: string): 'prefix' | 'suffix' | 'replace' | 'extension' | 'sequence' | 'none' => {
  if (original === newName) return 'none'
  
  const originalExt = getFileExtension(original)
  const newExt = getFileExtension(newName)
  const originalBase = getFileNameWithoutExtension(original)
  const newBase = getFileNameWithoutExtension(newName)
  
  if (originalExt !== newExt) return 'extension'
  if (newBase.startsWith(originalBase)) return 'suffix'
  if (newBase.endsWith(originalBase)) return 'prefix'
  if (/^\w+\d+$/.test(newBase)) return 'sequence'
  return 'replace'
}

// 生成新文件名
const generateNewName = (originalName: string, index: number, config: RenameConfig): string => {
  const extension = getFileExtension(originalName)
  const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.')) || originalName

  switch (config.type) {
    case 'regex':
      if (config.regexPattern && config.regexReplacement) {
        try {
          const regex = new RegExp(config.regexPattern, 'g')
          const newName = nameWithoutExt.replace(regex, config.regexReplacement)
          return extension ? `${newName}.${extension}` : newName
        } catch (error) {
          return originalName
        }
      }
      return originalName

    case 'prefix':
      return extension ? `${config.prefixText || ''}${nameWithoutExt}.${extension}` : `${config.prefixText || ''}${nameWithoutExt}`

    case 'suffix':
      return extension ? `${nameWithoutExt}${config.suffixText || ''}.${extension}` : `${nameWithoutExt}${config.suffixText || ''}`

    case 'replace':
      if (config.findText) {
        const newName = nameWithoutExt.replace(new RegExp(config.findText, 'g'), config.replaceText || '')
        return extension ? `${newName}.${extension}` : newName
      }
      return originalName

    case 'mapping':
      if (config.mappingFrom && config.mappingTo) {
        const fromList = parseMappingList(config.mappingFrom)
        const toList = parseMappingList(config.mappingTo)
        
        if (fromList.length !== toList.length) {
          return originalName
        }
        
        const mappingMap = new Map<string, string>()
        fromList.forEach((from, index) => {
          mappingMap.set(from, toList[index])
        })
        
        let newName = nameWithoutExt
        for (const [from, to] of mappingMap) {
          if (newName.includes(from)) {
            newName = newName.replace(new RegExp(from, 'g'), to)
            break
          }
        }
        
        return extension ? `${newName}.${extension}` : newName
      }
      return originalName

    case 'sequence':
      const number = ((config.startNumber || 1) + index).toString().padStart(config.numberPadding || 3, '0')
      return extension ? `${config.baseName || 'file'}${number}.${extension}` : `${config.baseName || 'file'}${number}`

    case 'extension':
      if (config.newExtension?.trim()) {
        const cleanExtension = config.newExtension.trim().replace(/^\.+/, '')
        return cleanExtension ? `${nameWithoutExt}.${cleanExtension}` : nameWithoutExt
      }
      return originalName

    default:
      return originalName
  }
}

// 批量计算重命名结果
const computeRenameResults = (files: FileItem[], config: RenameConfig): PreviewResult[] => {
  return files.map((file, index) => {
    const newName = generateNewName(file.original_name, index, config)
    return {
      original: file.original_name,
      new: newName,
      hasChanges: newName !== file.original_name,
      changeType: getChangeType(file.original_name, newName),
      index
    }
  })
}

// 时间切片处理大量数据
const processInChunks = async (
  files: FileItem[], 
  config: RenameConfig, 
  batchSize: number = 100,
  onProgress?: (progress: number, results: PreviewResult[]) => void
): Promise<PreviewResult[]> => {
  const results: PreviewResult[] = []
  const totalFiles = files.length
  
  for (let i = 0; i < totalFiles; i += batchSize) {
    const chunk = files.slice(i, i + batchSize)
    const chunkResults = computeRenameResults(chunk, config)
    results.push(...chunkResults)
    
    // 报告进度
    const progress = Math.min(100, Math.round(((i + batchSize) / totalFiles) * 100))
    if (onProgress) {
      onProgress(progress, results)
    }
    
    // 让出控制权，避免阻塞
    await new Promise(resolve => setTimeout(resolve, 0))
  }
  
  return results
}

// 增量计算：只计算变更的文件
const computeIncrementalResults = (
  files: FileItem[],
  config: RenameConfig,
  previousResults: PreviewResult[],
  changedIndices: Set<number>
): PreviewResult[] => {
  const results = [...previousResults]
  
  // 只重新计算变更的文件
  changedIndices.forEach(index => {
    if (index < files.length) {
      const file = files[index]
      const newName = generateNewName(file.original_name, index, config)
      results[index] = {
        original: file.original_name,
        new: newName,
        hasChanges: newName !== file.original_name,
        changeType: getChangeType(file.original_name, newName),
        index
      }
    }
  })
  
  return results
}

// 内存使用监控
const getMemoryUsage = (): number => {
  if ('memory' in performance) {
    return (performance as any).memory.usedJSHeapSize
  }
  return 0
}

// 性能监控数据
interface PerformanceMetrics {
  computeTime: number
  memoryUsage: number
  filesProcessed: number
  averageTimePerFile: number
}

// Worker 消息处理
self.onmessage = async (event) => {
  const { type, data } = event.data
  
  try {
    switch (type) {
      case 'COMPUTE_RENAME': {
        const { id, files, config, batchSize = 100 } = data as ComputeTask & { id: string }
        const startTime = performance.now()
        const startMemory = getMemoryUsage()
        
        // 使用时间切片处理
        const results = await processInChunks(
          files,
          config,
          batchSize,
          (progress, partialResults) => {
            // 发送进度更新
            self.postMessage({
              type: 'PROGRESS',
              data: {
                id,
                progress,
                partialResults: partialResults.slice(-batchSize) // 只发送最新的批次
              }
            })
          }
        )
        
        const endTime = performance.now()
        const endMemory = getMemoryUsage()
        
        const metrics: PerformanceMetrics = {
          computeTime: endTime - startTime,
          memoryUsage: endMemory - startMemory,
          filesProcessed: files.length,
          averageTimePerFile: (endTime - startTime) / files.length
        }
        
        self.postMessage({
          type: 'COMPUTE_COMPLETE',
          data: {
            id,
            results,
            metrics
          }
        })
        break
      }
      
      case 'COMPUTE_INCREMENTAL': {
        const { id, files, config, previousResults, changedIndices } = data
        const startTime = performance.now()
        
        const results = computeIncrementalResults(
          files,
          config,
          previousResults,
          new Set(changedIndices)
        )
        
        const endTime = performance.now()
        
        self.postMessage({
          type: 'INCREMENTAL_COMPLETE',
          data: {
            id,
            results,
            computeTime: endTime - startTime,
            changedCount: changedIndices.length
          }
        })
        break
      }
      
      case 'VALIDATE_CONFIG': {
        const { id, config } = data
        let isValid = true
        let errors: string[] = []
        
        // 验证正则表达式
        if (config.type === 'regex' && config.regexPattern) {
          try {
            new RegExp(config.regexPattern)
          } catch (error) {
            isValid = false
            errors.push('正则表达式格式不正确')
          }
        }
        
        // 验证映射数量
        if (config.type === 'mapping' && config.mappingFrom && config.mappingTo) {
          const fromList = parseMappingList(config.mappingFrom)
          const toList = parseMappingList(config.mappingTo)
          if (fromList.length !== toList.length) {
            isValid = false
            errors.push('映射列表数量不匹配')
          }
        }
        
        self.postMessage({
          type: 'VALIDATION_COMPLETE',
          data: {
            id,
            isValid,
            errors
          }
        })
        break
      }
      
      case 'GET_MEMORY_USAGE': {
        const memoryUsage = getMemoryUsage()
        self.postMessage({
          type: 'MEMORY_USAGE',
          data: {
            memoryUsage,
            timestamp: Date.now()
          }
        })
        break
      }
      
      default:
        console.warn('Unknown message type:', type)
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      data: {
        id: data.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    })
  }
}

// 导出类型定义（用于主线程）
export type {
  FileItem,
  RenameConfig,
  PreviewResult,
  ComputeTask,
  PerformanceMetrics
}