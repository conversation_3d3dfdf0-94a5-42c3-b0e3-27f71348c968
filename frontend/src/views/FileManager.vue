<template>
  <div class="file-manager-page">
    <!-- 文件管理横幅 -->
    <div class="file-manager-banner">
      <div class="banner-background">
        <div class="file-pattern"></div>
      </div>
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">文件管理中心</h1>
          <p class="banner-subtitle">系统唯一的文件上传入口 - 所有文件操作从这里开始</p>
          <div class="banner-actions">
            <el-button type="primary" size="large" @click="showUploadDialog = true" class="upload-main-btn">
              <el-icon><Upload /></el-icon>
              <span>上传文件</span>
              <span class="upload-hint">拖拽或点击上传</span>
            </el-button>
            <el-button size="large" @click="refreshFiles">
              <el-icon><Refresh /></el-icon>
              刷新列表
            </el-button>
            <el-button size="large" @click="showUploadGuide = true">
              <el-icon><QuestionFilled /></el-icon>
              上传指南
            </el-button>
          </div>
        </div>
        <div class="banner-visual">
          <div class="file-manager-icon">
            <el-icon><FolderOpened /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        文件统计
      </h2>
      <div class="stats-grid">
        <div class="stats-card primary">
          <div class="stats-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ files.length }}</div>
            <div class="stats-label">总文件数</div>
            <div class="stats-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>点击查看详情</span>
            </div>
          </div>
        </div>

        <div class="stats-card success">
          <div class="stats-icon">
            <el-icon><Select /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ selectedFiles.length }}</div>
            <div class="stats-label">已选择</div>
            <div class="stats-trend">
              <el-icon><Check /></el-icon>
              <span>已选中文件</span>
            </div>
          </div>
        </div>

        <div class="stats-card warning">
          <div class="stats-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ formatFileSize(totalSize) }}</div>
            <div class="stats-label">总大小</div>
            <div class="stats-trend">
              <el-icon><Monitor /></el-icon>
              <span>存储使用情况</span>
            </div>
          </div>
        </div>

        <div class="stats-card info">
          <div class="stats-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ recentFilesCount }}</div>
            <div class="stats-label">最近文件</div>
            <div class="stats-trend">
              <el-icon><Calendar /></el-icon>
              <span>24小时内上传</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">
        <el-icon><Lightning /></el-icon>
        快捷操作
      </h2>
      <div class="quick-actions-grid">
        <div class="quick-action-card" @click="showUploadDialog = true">
          <div class="action-icon">
            <el-icon><Upload /></el-icon>
          </div>
          <div class="action-content">
            <h3>上传文件</h3>
            <p>支持拖拽上传，多文件同时上传</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="createDirectory">
          <div class="action-icon">
            <el-icon><FolderAdd /></el-icon>
          </div>
          <div class="action-content">
            <h3>新建文件夹</h3>
            <p>创建新的文件夹来组织您的文件</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="viewMode = viewMode === 'table' ? 'grid' : 'table'">
          <div class="action-icon">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="action-content">
            <h3>切换视图</h3>
            <p>在列表视图和网格视图之间切换</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="$router.push('/batch-rename')" v-if="selectedFiles.length > 0">
          <div class="action-icon">
            <el-icon><Edit /></el-icon>
          </div>
          <div class="action-content">
            <h3>批量重命名</h3>
            <p>对选中的 {{ selectedFiles.length }} 个文件进行重命名</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件浏览器 -->
    <div class="file-browser-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><FolderOpened /></el-icon>
          文件浏览器
        </h2>
        <div class="browser-controls">
          <div class="search-container">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文件..."
              @input="handleSearch"
              style="width: 300px"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="showBatchSearchDialog = true" type="primary">
              <el-icon><DocumentCopy /></el-icon>
              批量搜索
            </el-button>
          </div>
          <el-select v-model="filterType" placeholder="文件类型" style="width: 120px" @change="handleFilterTypeChange">
            <el-option label="全部" value="" />
            <el-option label="图片" value="jpg,jpeg,png,gif" />
            <el-option label="文档" value="pdf,doc,docx,txt" />
            <el-option label="视频" value="mp4,avi,mov" />
          </el-select>
          <el-button-group>
            <el-button 
              :type="viewMode === 'table' ? 'primary' : ''" 
              @click="viewMode = 'table'"
            >
              <el-icon><List /></el-icon>
            </el-button>
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''" 
              @click="viewMode = 'grid'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <div class="browser-content">
        <!-- 批量操作栏 -->
        <div v-if="selectedFiles.length > 0" class="batch-actions-bar">
          <div class="selected-info">
            <el-icon><Select /></el-icon>
            已选择 {{ selectedFiles.length }} 个文件
          </div>
          <div class="batch-actions">
            <el-button @click="$router.push('/batch-rename')" type="primary" :disabled="batchDeleting">
              <el-icon><Edit /></el-icon>
              批量重命名
            </el-button>
            <el-button @click="batchDelete" type="danger" :loading="batchDeleting" :disabled="batchDeleting">
              <el-icon v-if="!batchDeleting"><Delete /></el-icon>
              {{ batchDeleting ? `删除中 ${deleteProgress}/${deleteTotal}` : '批量删除' }}
            </el-button>
            <el-button @click="selectedFiles = []" :disabled="batchDeleting">
              <el-icon><Close /></el-icon>
              取消选择
            </el-button>
          </div>
        </div>

        <!-- 批量删除进度条 -->
        <div v-if="batchDeleting" class="batch-delete-progress">
          <div class="progress-header">
            <h4>正在删除文件...</h4>
            <span class="progress-text">{{ deleteProgress }}/{{ deleteTotal }} 个文件</span>
          </div>
          <el-progress
            :percentage="Math.round((deleteProgress / deleteTotal) * 100)"
            :status="deleteErrors.length > 0 ? 'exception' : undefined"
            :stroke-width="8"
            class="delete-progress-bar"
          />
          <div v-if="deleteErrors.length > 0" class="delete-errors">
            <el-alert
              v-for="(error, index) in deleteErrors.slice(-3)"
              :key="index"
              :title="error"
              type="error"
              :closable="false"
              show-icon
            />
          </div>
        </div>

        <!-- 表格视图 - 优化版 -->
        <div v-if="viewMode === 'table'" class="table-container">
          <el-table
            :data="files"
            v-loading="loading"
            @selection-change="handleSelectionChange"
            empty-text="暂无文件数据"
            :row-class-name="tableRowClassName"
            stripe
          >
            <el-table-column type="selection" width="45" />
            <el-table-column label="文件名" min-width="300">
              <template #default="{ row }">
                <div class="file-info-enhanced">
                  <div class="file-icon-box">
                    <el-icon :size="32" :style="{ color: getFileIconColor(getFileExtension(row.original_name)) }">
                      <component :is="getFileIcon(getFileExtension(row.original_name))" />
                    </el-icon>
                    <span class="file-ext-badge">{{ getFileExtension(row.original_name).toUpperCase() }}</span>
                  </div>
                  <div class="file-details">
                    <span class="file-name-text" :title="row.original_name">{{ row.original_name }}</span>
                    <span class="file-meta-info">
                      {{ formatFileSize(row.size) }} · {{ getRelativeTime(row.created_at) }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="120" sortable>
              <template #default="{ row }">
                <span class="size-text">{{ formatFileSize(row.size) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="上传时间" width="180" sortable>
              <template #default="{ row }">
                <div class="time-info">
                  <span class="time-date">{{ formatDate(row.created_at).split(' ')[0] }}</span>
                  <span class="time-clock">{{ formatDate(row.created_at).split(' ')[1] }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="160" fixed="right">
              <template #default="{ row }">
                <div class="table-actions">
                  <el-button
                    size="small"
                    @click="downloadFile(row)"
                    link
                    type="primary"
                    :disabled="deletingFiles.has(row.id)"
                  >
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  <el-dropdown trigger="click" @command="(cmd) => handleFileCommand(cmd, row)" :disabled="deletingFiles.has(row.id)">
                    <el-button size="small" link :disabled="deletingFiles.has(row.id)">
                      <el-icon v-if="deletingFiles.has(row.id)" class="rotating"><Loading /></el-icon>
                      <el-icon v-else><More /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :icon="Edit" command="rename">重命名</el-dropdown-item>
                        <el-dropdown-item :icon="CopyDocument" command="copy">复制</el-dropdown-item>
                        <el-dropdown-item :icon="FolderAdd" command="move">移动</el-dropdown-item>
                        <el-dropdown-item divided :icon="Delete" command="delete">
                          <span style="color: #f56c6c">删除</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 网格视图 -->
        <div v-else class="file-grid-container">
          <div v-if="files.length === 0 && !loading" class="empty-state">
            <div class="empty-illustration">
              <div class="empty-icon">
                <el-icon><DocumentRemove /></el-icon>
              </div>
              <div class="empty-content">
                <h3>还没有文件</h3>
                <p>开始上传您的第一个文件</p>
                <el-button type="primary" @click="showUploadDialog = true">
                  立即上传
                </el-button>
              </div>
            </div>
          </div>
          
          <div v-else class="file-grid" v-loading="loading">
            <div 
              v-for="file in files" 
              :key="file.id"
              class="file-item"
              :class="{ selected: selectedFiles.some(f => f.id === file.id) }"
              @click="toggleFileSelection(file)"
            >
              <div class="file-item-icon">
                <el-icon size="48">
                  <component :is="getFileIcon(getFileExtension(file.original_name))" />
                </el-icon>
              </div>
              <div class="file-item-name">{{ file.original_name }}</div>
              <div class="file-item-info">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <div class="file-item-actions">
                <el-button size="small" @click.stop="downloadFile(file)" type="primary" :disabled="deletingFiles.has(file.id)">
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="deleteFile(file)" type="danger" :loading="deletingFiles.has(file.id)" :disabled="deletingFiles.has(file.id)">
                  <el-icon v-if="!deletingFiles.has(file.id)"><Delete /></el-icon>
                  {{ deletingFiles.has(file.id) ? '删除中' : '' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页和加载更多 -->
        <div v-if="total > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="smartPageSizes"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handleCurrentPageChange"
            background
          />
          
          <!-- 加载更多按钮 -->
          <div v-if="hasMoreFiles" class="load-more-container">
            <el-button
              @click="loadMoreFiles"
              :loading="loadingMore"
              type="primary"
              size="large"
              class="load-more-btn"
            >
              <el-icon v-if="!loadingMore"><ArrowDown /></el-icon>
              {{ loadingMore ? '加载中...' : '加载更多文件' }}
            </el-button>
            <div class="load-more-info">
              已显示 {{ files.length }} / {{ total }} 个文件
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文件"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="!isUploading"
      :show-close="!isUploading"
    >
      <div class="upload-dialog-content">
        <!-- 文件类型过滤器 -->
        <div class="upload-filters" v-if="!isUploading && uploadFileList.length === 0">
          <div class="filter-section">
            <h4>文件类型过滤</h4>
            <el-checkbox-group v-model="allowedFileTypes" @change="handleFileTypeChange">
              <el-checkbox label="image">图片文件 (jpg, png, gif, webp)</el-checkbox>
              <el-checkbox label="document">文档文件 (pdf, doc, docx, txt)</el-checkbox>
              <el-checkbox label="video">视频文件 (mp4, avi, mov, mkv)</el-checkbox>
              <el-checkbox label="audio">音频文件 (mp3, wav, flac, aac)</el-checkbox>
              <el-checkbox label="archive">压缩文件 (zip, rar, 7z, tar)</el-checkbox>
              <el-checkbox label="other">其他文件</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 上传区域 -->
        <div class="upload-area" v-if="!isUploading && uploadFileList.length === 0">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :on-progress="handleUploadProgress"
            :on-change="handleFileChange"
            name="files"
            multiple
            drag
            class="modern-upload"
            :auto-upload="false"
            :accept="acceptedFileTypes"
            :show-file-list="false"
          >
            <div class="upload-content">
              <div class="upload-animation">
                <el-icon class="upload-icon"><UploadFilled /></el-icon>
                <div class="upload-ripple"></div>
              </div>
              <div class="upload-text">
                <div class="upload-title">拖拽文件到此处上传</div>
                <div class="upload-subtitle">或 <em>点击选择文件</em></div>
                <div class="upload-formats">
                  支持所有常见文件格式
                </div>
              </div>
            </div>
            <template #tip>
              <div class="upload-tips">
                <div class="tip-item success">
                  <el-icon><Check /></el-icon>
                  <span>支持批量上传，可同时选择多个文件</span>
                </div>
                <div class="tip-item warning">
                  <el-icon><Warning /></el-icon>
                  <span>单个文件大小限制：100MB</span>
                </div>
                <div class="tip-item info">
                  <el-icon><InfoFilled /></el-icon>
                  <span>上传后可进行重命名、下载、删除等操作</span>
                </div>
                <div class="tip-item-plain">
                  <el-icon><Star /></el-icon>
                  <span>系统唯一的文件上传入口 - 所有文件操作从这里开始</span>
                </div>
              </div>
            </template>
          </el-upload>
        </div>

        <!-- 现代化的文件列表和进度显示 -->
        <div v-if="uploadFileList.length > 0" class="modern-upload-section">
          <!-- 优雅的头部信息 -->
          <div class="upload-header">
            <div class="header-top">
              <h4 class="header-title">
                <el-icon :class="{ 'rotating': isUploading }">
                  <component :is="isUploading ? Loading : Document" />
                </el-icon>
                {{ isUploading ? '正在上传' : '待上传文件' }}
              </h4>
              <div class="header-actions" v-if="!isUploading">
                <el-button @click="clearFileList" :icon="Delete" round>
                  清空列表
                </el-button>
                <el-button type="primary" @click="startUpload" :icon="Upload" round>
                  <span>开始上传</span>
                  <span class="upload-count">{{ validFilesCount }}</span>
                </el-button>
              </div>
            </div>
            <div class="header-stats">
              <span class="stat-badge total">
                <el-icon><Files /></el-icon>
                <span>{{ uploadFileList.length }}个文件</span>
              </span>
              <span class="stat-badge size">
                <el-icon><DataBoard /></el-icon>
                <span>{{ formatTotalSize(uploadFileList) }}</span>
              </span>
              <span v-if="isCheckingDuplicates" class="stat-badge info">
                <el-icon class="rotating"><Loading /></el-icon>
                检测重复文件中...
              </span>
              <span v-else-if="duplicateFiles.length > 0" class="stat-badge warning">
                <el-icon><Warning /></el-icon>
                {{ duplicateFiles.length }} 个重复
              </span>
            </div>
          </div>

          <!-- 上传中显示整体进度 -->
          <div v-if="isUploading" class="uploading-view">
            <!-- 总体进度条 -->
            <div class="overall-progress">
              <div class="progress-info">
                <span class="progress-text">总进度: {{ Math.round(overallProgress) }}%</span>
                <span class="files-count">{{ completedFiles + errorFiles }}/{{ totalFiles }}个文件</span>
              </div>
              <el-progress
                :percentage="Math.round(overallProgress)"
                :status="uploadStatus"
                :stroke-width="12"
                class="main-progress"
              />
            </div>

            <!-- 当前上传状态 -->
            <div class="current-status">
              <div class="status-info">
                <div class="current-file">
                  <el-icon class="rotating"><Loading /></el-icon>
                  <span>正在上传: {{ currentUploadingFile }}</span>
                </div>
                <div class="upload-stats">
                  <span>上传速度: {{ averageSpeed }}</span>
                  <span>剩余时间: {{ estimatedTime }}</span>
                </div>
              </div>
            </div>

            <!-- 简化的统计信息 -->
            <div class="upload-summary">
              <div class="summary-item success" v-if="completedFiles > 0">
                <el-icon><Check /></el-icon>
                <span>已完成: {{ completedFiles }}</span>
              </div>
              <div class="summary-item error" v-if="errorFiles > 0">
                <el-icon><Close /></el-icon>
                <span>失败: {{ errorFiles }}</span>
              </div>
              <div class="summary-item uploading" v-if="uploadingFiles > 0">
                <el-icon><Loading /></el-icon>
                <span>上传中: {{ uploadingFiles }}</span>
              </div>
            </div>
          </div>

          <!-- 文件列表视图 - 只在非上传状态显示 -->
          <div v-if="!isUploading" class="modern-file-list">
            <!-- 快捷操作栏 -->
            <div class="quick-actions-bar" v-if="duplicateFiles.length > 0 || uploadFileList.length > 5">
              <el-dropdown v-if="duplicateFiles.length > 0" @command="handleDuplicateAction">
                <el-button
                  :icon="Warning"
                  type="warning"
                  plain
                  round
                  size="small"
                >
                  处理重复文件 ({{ duplicateFiles.length }})
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="skip">
                      <el-icon><Close /></el-icon>
                      跳过重复文件
                    </el-dropdown-item>
                    <el-dropdown-item command="rename">
                      <el-icon><Edit /></el-icon>
                      自动重命名上传
                    </el-dropdown-item>
                    <el-dropdown-item command="replace" divided>
                      <el-icon><Refresh /></el-icon>
                      替换已存在文件
                    </el-dropdown-item>
                    <el-dropdown-item command="view-details">
                      <el-icon><View /></el-icon>
                      查看重复详情
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button
                v-if="uploadFileList.length > 5"
                @click="toggleFileListView"
                :icon="showAllFiles ? ArrowUp : ArrowDown"
                plain
                round
                size="small"
              >
                {{ showAllFiles ? '收起' : `展开查看 (${uploadFileList.length})` }}
              </el-button>
            </div>

            <!-- 美化的文件列表 -->
            <div class="modern-file-grid" :class="{ 'collapsed': !showAllFiles && uploadFileList.length > 5 }">
              <!-- 紧凑模式的文件列表 -->
              <div v-if="!showAllFiles && uploadFileList.length > 5" class="compact-file-list">
                <div class="compact-file-grid">
                  <div
                    v-for="(file, index) in uploadFileList.slice(0, 5)"
                    :key="`${file.name}-${index}`"
                    class="compact-file-item"
                    :class="{
                      'is-duplicate': file.isDuplicate
                    }"
                  >
                    <el-icon class="compact-file-icon">
                      <component :is="getFileIcon(getFileExtension(file.name))" />
                    </el-icon>
                    <span class="compact-file-name" :title="file.name">{{ file.name }}</span>
                    <span class="compact-file-size">{{ formatFileSize(file.size) }}</span>
                    <el-button
                      @click="removeFile(index)"
                      :icon="Close"
                      link
                      type="danger"
                      size="small"
                    />
                  </div>
                </div>
              </div>
              
              <!-- 文件数量 <= 5 且未展开时的紧凑列表 -->
              <div v-else-if="!showAllFiles && uploadFileList.length <= 5" class="compact-file-list">
                <div class="compact-file-grid">
                  <div
                    v-for="(file, index) in uploadFileList"
                    :key="`${file.name}-${index}`"
                    class="compact-file-item"
                    :class="{
                      'is-duplicate': file.isDuplicate
                    }"
                  >
                    <el-icon class="compact-file-icon">
                      <component :is="getFileIcon(getFileExtension(file.name))" />
                    </el-icon>
                    <span class="compact-file-name" :title="file.name">{{ file.name }}</span>
                    <span class="compact-file-size">{{ formatFileSize(file.size) }}</span>
                    <el-button
                      @click="removeFile(index)"
                      :icon="Close"
                      link
                      type="danger"
                      size="small"
                    />
                  </div>
                </div>
              </div>
              
              <!-- 展开模式的文件列表 - 带滚动 -->
              <div v-else-if="showAllFiles" class="expanded-file-view">
                <!-- 文件数量提示 -->
                <div class="file-list-header">
                  <div class="file-count-info">
                    <el-icon><Files /></el-icon>
                    <span>{{ uploadFileList.length }}个文件</span>
                    <span class="separator">·</span>
                    <el-icon><DataBoard /></el-icon>
                    <span>{{ formatTotalSize(uploadFileList) }}</span>
                  </div>
                  <el-input
                    v-if="uploadFileList.length > 10"
                    v-model="fileSearchQuery"
                    placeholder="搜索文件名..."
                    :prefix-icon="Search"
                    size="small"
                    clearable
                    style="width: 200px"
                  />
                </div>
                
                <!-- 可滚动的文件列表容器 -->
                <div class="scrollable-file-list">
                  <TransitionGroup name="file-list" tag="div" class="file-list-container">
                    <div
                      v-for="(file, index) in filteredUploadFiles"
                      :key="`${file.name}-${index}`"
                      class="modern-file-card"
                      :class="{
                        'is-duplicate': file.isDuplicate,
                        'is-large': file.size > 50 * 1024 * 1024
                      }"
                      :data-duplicate-type="file.duplicateType"
                    >
                  <!-- 文件图标 -->
                  <div class="file-icon-wrapper">
                    <div class="file-icon-bg"></div>
                    <el-icon class="file-main-icon">
                      <component :is="getFileIcon(getFileExtension(file.name))" />
                    </el-icon>
                    <div class="file-ext">{{ getFileExtension(file.name).toUpperCase() }}</div>
                  </div>
                  
                  <!-- 文件信息 -->
                  <div class="file-content">
                    <div class="file-name-row">
                      <span class="file-name" :title="file.name">{{ file.name }}</span>
                      <el-tag
                        v-if="file.isDuplicate"
                        :type="file.duplicateType === 'content' ? 'danger' : 'warning'"
                        size="small"
                        effect="plain"
                        :title="getDuplicateTooltip(file)"
                      >
                        <el-icon><Warning /></el-icon>
                        {{ getDuplicateLabel(file.duplicateType) }}
                      </el-tag>
                    </div>
                    <div class="file-meta-row">
                      <span class="meta-item">
                        <el-icon><DataBoard /></el-icon>
                        {{ formatFileSize(file.size) }}
                      </span>
                      <span class="meta-item">
                        <el-icon><Document /></el-icon>
                        {{ getFileTypeLabel(file.name) }}
                      </span>
                    </div>
                  </div>
                  
                      <!-- 操作按钮 -->
                      <div class="file-action">
                        <el-button
                          @click="removeFile(index)"
                          :icon="Close"
                          circle
                          plain
                          type="danger"
                          size="small"
                        />
                      </div>
                    </div>
                  </TransitionGroup>
                </div>
              </div>
              
              <!-- 优雅的折叠提示 -->
              <Transition name="fade">
                <div v-if="!showAllFiles && uploadFileList.length > 5" class="modern-collapsed-hint">
                  <div class="hint-decoration"></div>
                  <div class="hint-content">
                    <el-icon class="hint-icon"><MoreFilled /></el-icon>
                    <span class="hint-text">还有 <strong>{{ uploadFileList.length - 5 }}</strong>个文件</span>
                    <el-button
                      type="primary"
                      @click="showAllFiles = true"
                      size="small"
                      round
                    >
                      查看全部
                    </el-button>
                  </div>
                </div>
              </Transition>
            </div>
          </div>
          
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="!isUploading && uploadFileList.length === 0"
            @click="showUploadDialog = false"
          >
            取消
          </el-button>
          <el-button
            v-if="isUploading"
            type="danger"
            @click="cancelUpload"
          >
            取消上传
          </el-button>
          <el-button
            v-if="!isUploading && completedFiles > 0"
            type="success"
            @click="finishUpload"
          >
            完成
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 上传指南对话框 -->
    <el-dialog
      v-model="showUploadGuide"
      title="文件上传指南"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="upload-guide-content">
        <div class="guide-section">
          <h3>
            <el-icon><Document /></el-icon>
            支持的文件类型
          </h3>
          <div class="file-types-grid">
            <div class="file-type-item">
              <el-icon><Picture /></el-icon>
              <span>图片</span>
              <small>JPG, PNG, GIF, WebP</small>
            </div>
            <div class="file-type-item">
              <el-icon><Document /></el-icon>
              <span>文档</span>
              <small>PDF, DOC, TXT, XLS</small>
            </div>
            <div class="file-type-item">
              <el-icon><VideoPlay /></el-icon>
              <span>视频</span>
              <small>MP4, AVI, MOV, MKV</small>
            </div>
            <div class="file-type-item">
              <el-icon><Headset /></el-icon>
              <span>音频</span>
              <small>MP3, WAV, FLAC, AAC</small>
            </div>
          </div>
        </div>
        
        <div class="guide-section">
          <h3>
            <el-icon><Lightning /></el-icon>
            快捷操作
          </h3>
          <ul class="guide-list">
            <li>支持拖拽文件或文件夹到上传区域</li>
            <li>支持 Ctrl/Cmd + V 粘贴文件上传</li>
            <li>支持批量选择多个文件同时上传</li>
            <li>上传完成后可直接进行批量重命名</li>
          </ul>
        </div>
        
        <div class="guide-section">
          <h3>
            <el-icon><Warning /></el-icon>
            注意事项
          </h3>
          <ul class="guide-list warning">
            <li>单个文件大小不能超过 100MB</li>
            <li>请勿上传包含敏感信息的文件</li>
            <li>大文件上传时请保持网络连接稳定</li>
            <li>同名文件会自动重命名避免覆盖</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <el-button type="primary" @click="showUploadGuide = false">我知道了</el-button>
      </template>
    </el-dialog>

    <!-- 批量搜索对话框 -->
    <el-dialog
      v-model="showBatchSearchDialog"
      title="批量搜索文件"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-search-content">
        <div class="search-instructions">
          <h4>使用说明</h4>
          <ul>
            <li>每行输入一个搜索关键词</li>
            <li>支持粘贴多个关键词（自动按行分割）</li>
            <li>将搜索所有包含任意关键词的文件</li>
            <li>最多支持100个关键词</li>
          </ul>
        </div>
        
        <el-input
          v-model="batchSearchText"
          type="textarea"
          :rows="10"
          placeholder="请输入搜索关键词，每行一个&#10;例如：&#10;9200190392641700162321&#10;document.pdf&#10;image.jpg"
          @input="handleBatchSearchInput"
        />
        
        <div class="search-stats">
          <span>关键词数量: {{ batchSearchKeywords.length }}</span>
          <span v-if="batchSearchKeywords.length > 100" class="error-text">
            超出限制！最多支持100个关键词
          </span>
        </div>
        
        <div v-if="batchSearchKeywords.length > 0" class="keywords-preview">
          <h5>关键词预览:</h5>
          <div class="keywords-list">
            <el-tag
              v-for="(keyword, index) in batchSearchKeywords.slice(0, 10)"
              :key="index"
              size="small"
              class="keyword-tag"
            >
              {{ keyword }}
            </el-tag>
            <el-tag v-if="batchSearchKeywords.length > 10" size="small" type="info">
              +{{ batchSearchKeywords.length - 10 }} 更多...
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchSearchDialog = false">取消</el-button>
          <el-button @click="clearBatchSearch">清空</el-button>
          <el-button
            type="primary"
            @click="executeBatchSearch"
            :disabled="batchSearchKeywords.length === 0 || batchSearchKeywords.length > 100"
          >
            搜索 ({{ batchSearchKeywords.length }} 个关键词)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 防抖工具函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout | null = null
  return function executedFunction(...args: any[]) {
    const later = () => {
      timeout = null
      func(...args)
    }
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}
import {
  Upload,
  FolderAdd,
  Lightning,
  ArrowRight,
  ArrowDown,
  ArrowUp,
  Edit,
  Grid,
  FolderOpened,
  Search,
  List,
  Select,
  Close,
  Download,
  Delete,
  DocumentRemove,
  UploadFilled,
  InfoFilled,
  Warning,
  Check,
  Picture,
  Document,
  VideoPlay,
  DataAnalysis,
  TrendCharts,
  DataBoard,
  Monitor,
  Clock,
  Calendar,
  Loading,
  DocumentCopy,
  MoreFilled,
  Files,
  QuestionFilled,
  Star,
  Headset,
  Refresh,
  More,
  CopyDocument
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize, downloadFile as downloadFileUtil } from '@/utils/api'
import { smartDuplicateDetection } from '@/utils/fileHash'
import type { FileItem } from '@/types'

const authStore = useAuthStore()

const files = ref<FileItem[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const searchQuery = ref('')
const filterType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedFiles = ref<FileItem[]>([])
const showUploadDialog = ref(false)
const showUploadGuide = ref(false)
const viewMode = ref<'table' | 'grid'>('table')
const hasMoreFiles = ref(true)

// 删除状态管理
const deletingFiles = ref<Set<number>>(new Set())
const batchDeleting = ref(false)
const deleteProgress = ref(0)
const deleteTotal = ref(0)
const deleteErrors = ref<string[]>([])

// 批量搜索相关状态
const showBatchSearchDialog = ref(false)
const batchSearchText = ref('')
const batchSearchKeywords = ref<string[]>([])

// 上传进度相关状态
const isUploading = ref(false)
const uploadFileList = ref<any[]>([])
const currentUploadingFile = ref('')
const uploadRef = ref()

// 文件列表显示控制
const showAllFiles = ref(false)
const fileListPage = ref(1)
const fileListPageSize = ref(20) // 每页显示20个文件
const fileSearchQuery = ref('') // 文件搜索查询

// 新增的上传优化相关状态
const allowedFileTypes = ref<string[]>(['image', 'document', 'video', 'audio', 'archive', 'other'])
const maxFileCount = ref(10000) // 支持大批量文件上传
const maxFileSize = ref(100 * 1024 * 1024) // 100MB
const duplicateFiles = ref<string[]>([]) // 重复文件列表

// 新增：智能重复检测相关状态
const isCheckingDuplicates = ref(false) // 是否正在检查重复文件
const duplicateCheckProgress = ref(0) // 重复检查进度
const duplicateCheckTotal = ref(0) // 重复检查总数
const duplicateCheckCurrentFile = ref('') // 当前检查的文件

// 上传统计
const completedFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'success').length
)
const totalFiles = computed(() => uploadFileList.value.length)
const overallProgress = computed(() => {
  if (totalFiles.value === 0) return 0
  // 基于已完成文件数量计算整体进度
  return (completedFiles.value / totalFiles.value) * 100
})
const uploadStatus = computed(() => {
  if (completedFiles.value === totalFiles.value && totalFiles.value > 0) return 'success'
  if (uploadFileList.value.some(file => file.status === 'error')) return 'exception'
  return undefined
})

// 新增的计算属性
const errorFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'error').length
)

const uploadingFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'uploading').length
)

const displayedFiles = computed(() => {
  if (showAllFiles.value) {
    return uploadFileList.value
  }
  return uploadFileList.value.slice(0, 5)
})

// 分页后的文件列表
const paginatedFiles = computed(() => {
  const start = (fileListPage.value - 1) * fileListPageSize.value
  const end = start + fileListPageSize.value
  return uploadFileList.value.slice(start, end)
})

// 过滤后的上传文件列表
const filteredUploadFiles = computed(() => {
  if (!fileSearchQuery.value) {
    return uploadFileList.value
  }
  const query = fileSearchQuery.value.toLowerCase()
  return uploadFileList.value.filter(file =>
    file.name.toLowerCase().includes(query)
  )
})

// 有效文件数量计算
const validFilesCount = computed(() => {
  return uploadFileList.value.filter(file => !file.isDuplicate).length
})

// 文件选择阶段显示的文件列表
const displayedSelectionFiles = computed(() => {
  if (showAllFiles.value) {
    return uploadFileList.value
  }
  return uploadFileList.value.slice(0, 3)
})

// 计算总文件大小的方法
const formatTotalSize = (fileList: any[]) => {
  const totalBytes = fileList.reduce((sum, file) => sum + (file.size || 0), 0)
  return formatFileSize(totalBytes)
}

// 移除文件的方法
const removeFile = (index: number) => {
  const removedFile = uploadFileList.value[index]
  uploadFileList.value.splice(index, 1)
  
  // 同时更新Element Plus的文件列表
  if (uploadRef.value && uploadRef.value.uploadFiles) {
    uploadRef.value.uploadFiles.splice(index, 1)
  }
  
  // 重新检查重复文件
  updateDuplicateFiles()
  
  // ElMessage.info(`已移除文件: ${removedFile.name}`)
}

// 从分页列表中移除文件
const removeFileFromPage = (pageIndex: number) => {
  // 计算实际的文件索引
  const actualIndex = (fileListPage.value - 1) * fileListPageSize.value + pageIndex
  removeFile(actualIndex)
  
  // 如果当前页没有文件了，返回上一页
  const currentPageStart = (fileListPage.value - 1) * fileListPageSize.value
  if (currentPageStart >= uploadFileList.value.length && fileListPage.value > 1) {
    fileListPage.value--
  }
}

// 智能重复文件检测（支持内容检测）
const updateDuplicateFiles = async () => {
  if (uploadFileList.value.length === 0) {
    duplicateFiles.value = []
    return
  }

  isCheckingDuplicates.value = true
  duplicateCheckProgress.value = 0
  duplicateCheckTotal.value = uploadFileList.value.length

  try {
    // 第一步：快速检查文件名重复
    const duplicates: string[] = []
    const existingNames = files.value.map(f => f.original_name)
    const currentFileNames = new Map<string, number>()

    // 检查当前待上传文件列表中的重复
    uploadFileList.value.forEach(file => {
      const count = currentFileNames.get(file.name) || 0
      currentFileNames.set(file.name, count + 1)
    })

    // 标记文件名重复
    uploadFileList.value.forEach(file => {
      const isNameDuplicate =
        existingNames.includes(file.name) || // 与服务器文件重复
        (currentFileNames.get(file.name) || 0) > 1 // 在当前列表中重复

      if (isNameDuplicate) {
        file.isDuplicate = true
        file.duplicateType = 'name'
        if (!duplicates.includes(file.name)) {
          duplicates.push(file.name)
        }
      } else {
        file.isDuplicate = false
        file.duplicateType = undefined
      }
    })

    // 第二步：对非重复文件进行服务器端检查（文件名+大小）
    const nonDuplicateFiles = uploadFileList.value.filter(f => !f.isDuplicate)

    if (nonDuplicateFiles.length > 0) {
      // 准备文件信息用于服务器检查（不包含hash，避免前端计算）
      const fileInfos = nonDuplicateFiles.map(f => ({
        name: f.name,
        size: f.size
        // 注意：不计算hash，保持性能
      }))

      try {
        const response = await apiMethods.files.checkDuplicates({ files: fileInfos })

        if (response.data.success) {
          const duplicateResults = response.data.data

          // 更新重复状态
          duplicateResults.forEach((result: any) => {
            const fileItem = uploadFileList.value.find(f => f.name === result.name)
            if (fileItem && result.is_duplicate) {
              fileItem.isDuplicate = true
              fileItem.duplicateType = result.duplicate_type
              fileItem.existingFile = result.existing_file

              if (!duplicates.includes(result.name)) {
                duplicates.push(result.name)
              }
            }
          })
        }
      } catch (error) {
        console.warn('服务器重复检测失败，使用本地检测:', error)
      }
    }

    duplicateFiles.value = duplicates

  } finally {
    isCheckingDuplicates.value = false
    duplicateCheckProgress.value = duplicateCheckTotal.value
  }
}

// 上传速度和时间估算
const averageSpeed = ref('0 KB/s')
const estimatedTime = ref('--')
const uploadStartTime = ref(0)
const uploadedBytes = ref(0)

const uploadUrl = computed(() => '/api/v1/files/upload')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

// 文件类型相关计算属性
const acceptedFileTypes = computed(() => {
  const typeMap: Record<string, string> = {
    image: '.jpg,.jpeg,.png,.gif,.webp,.svg',
    document: '.pdf,.doc,.docx,.txt,.md',
    video: '.mp4,.avi,.mov,.mkv,.webm',
    audio: '.mp3,.wav,.flac,.aac,.ogg',
    archive: '.zip,.rar,.7z,.tar,.gz',
    other: '*'
  }
  
  if (allowedFileTypes.value.length === 0) return '*'
  
  return allowedFileTypes.value
    .map(type => typeMap[type] || '')
    .filter(Boolean)
    .join(',')
})

// 计算总文件大小
const totalSize = computed(() => {
  return files.value.reduce((total, file) => total + (file.size || 0), 0)
})

// 计算最近文件数量（24小时内）
const recentFilesCount = computed(() => {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
  return files.value.filter(file => {
    const fileDate = new Date(file.created_at)
    return fileDate > oneDayAgo
  }).length
})

// 智能分页大小计算
const smartPageSizes = computed(() => {
  const totalCount = total.value
  const baseSizes = [10, 20, 50]
  
  if (totalCount <= 50) {
    // 小数据量：提供基础选项
    return baseSizes
  } else if (totalCount <= 200) {
    // 中等数据量：添加100选项
    return [...baseSizes, 100]
  } else if (totalCount <= 500) {
    // 大数据量：添加200和500选项
    return [...baseSizes, 100, 200, 500]
  } else if (totalCount <= 1000) {
    // 超大数据量：添加更多选项，包括"全部"
    return [...baseSizes, 100, 200, 500, 1000, totalCount]
  } else if (totalCount <= 5000) {
    // 海量数据：提供更灵活的选项
    return [...baseSizes, 100, 200, 500, 1000, 2000, Math.ceil(totalCount / 2), totalCount]
  } else {
    // 超海量数据：提供分层选项，避免性能问题
    const quarter = Math.ceil(totalCount / 4)
    const half = Math.ceil(totalCount / 2)
    return [...baseSizes, 100, 200, 500, 1000, 2000, quarter, half, totalCount]
  }
})

const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

const getFileIcon = (extension: string) => {
  const ext = extension?.toLowerCase() || ''
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) {
    return Picture
  } else if (['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)) {
    return Document
  } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].includes(ext)) {
    return VideoPlay
  } else if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)) {
    return Document // 使用Document作为音频文件的图标
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return Document
  } else if (['js', 'ts', 'vue', 'html', 'css', 'json', 'xml'].includes(ext)) {
    return Document
  }
  return Document
}

// 获取文件类型标签
const getFileTypeLabel = (filename: string): string => {
  const ext = getFileExtension(filename)
  const typeMap: Record<string, string> = {
    jpg: '图片', jpeg: '图片', png: '图片', gif: '图片', webp: '图片', svg: '图片',
    pdf: '文档', doc: '文档', docx: '文档', txt: '文本', md: '文档',
    mp4: '视频', avi: '视频', mov: '视频', mkv: '视频', webm: '视频',
    mp3: '音频', wav: '音频', flac: '音频', aac: '音频', ogg: '音频',
    zip: '压缩', rar: '压缩', '7z': '压缩', tar: '压缩', gz: '压缩',
    js: '代码', ts: '代码', vue: '代码', html: '代码', css: '代码', json: '配置'
  }
  return typeMap[ext] || '其他'
}

// 清除无效文件
const clearInvalidFiles = () => {
  const beforeCount = uploadFileList.value.length
  const duplicateCount = uploadFileList.value.filter(file => file.isDuplicate).length

  if (duplicateCount === 0) {
    ElMessage.info('没有重复文件需要清除')
    return
  }

  // 过滤掉重复文件
  uploadFileList.value = uploadFileList.value.filter(file => !file.isDuplicate)

  // 同时更新Element Plus的文件列表
  if (uploadRef.value && uploadRef.value.uploadFiles) {
    const validFileNames = uploadFileList.value.map(f => f.name)
    uploadRef.value.uploadFiles = uploadRef.value.uploadFiles.filter((file: any) => {
      return validFileNames.includes(file.name)
    })
  }

  // 清空重复文件列表
  duplicateFiles.value = []

  const afterCount = uploadFileList.value.length
  const removedCount = beforeCount - afterCount

  ElMessage.success(`已跳过 ${removedCount} 个重复文件`)
}

const loadFiles = async (append = false) => {
  try {
    if (append) {
      loadingMore.value = true
    } else {
      loading.value = true
      files.value = []
    }
    
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      filterType: filterType.value
    }
    
    const response = await apiMethods.files.list(params)
    const newFiles = response.data.data.files
    
    if (append) {
      files.value = [...files.value, ...newFiles]
    } else {
      files.value = newFiles
    }
    
    total.value = response.data.data.pagination.total
    hasMoreFiles.value = files.value.length < total.value
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const loadMoreFiles = async () => {
  if (loadingMore.value || !hasMoreFiles.value) return
  currentPage.value++
  await loadFiles(true)
}

const handleSearch = () => {
  // 搜索时重置到第一页
  currentPage.value = 1
  loadFiles()
}

const handleFilterTypeChange = () => {
  // 文件类型过滤变化时重置到第一页
  currentPage.value = 1
  loadFiles()
}


const handlePageSizeChange = (newPageSize: number) => {
  // 保存当前滚动位置
  const currentScrollY = window.scrollY
  
  pageSize.value = newPageSize
  currentPage.value = 1
  loadFiles().then(() => {
    // 数据加载完成后，恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, currentScrollY)
    })
  })
}

const handleCurrentPageChange = (newPage: number) => {
  // 保存当前滚动位置
  const currentScrollY = window.scrollY
  
  currentPage.value = newPage
  loadFiles(false).then(() => {
    // 数据加载完成后，恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, currentScrollY)
    })
  })
}

const handleSelectionChange = (selection: FileItem[]) => {
  selectedFiles.value = selection
}

const downloadFile = async (file: FileItem) => {
  try {
    const response = await apiMethods.files.download(file.id)
    downloadFileUtil(response.data, file.original_name)
    // ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

const deleteFile = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.original_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 添加到删除中状态
    deletingFiles.value.add(file.id)
    
    try {
      await apiMethods.files.delete(file.id)
      ElMessage.success(`文件 "${file.original_name}" 删除成功`)
      loadFiles()
    } catch (deleteError) {
      console.error('删除文件失败:', deleteError)
      ElMessage.error(`删除文件 "${file.original_name}" 失败`)
    } finally {
      // 移除删除中状态
      deletingFiles.value.delete(file.id)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
    }
  }
}

const createDirectory = async () => {
  try {
    const { value: name } = await ElMessageBox.prompt(
      '请输入文件夹名称',
      '新建文件夹',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    if (name) {
      await apiMethods.directories.create({ name })
      // ElMessage.success('文件夹创建成功')
      loadFiles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建文件夹失败:', error)
      ElMessage.error('创建文件夹失败')
    }
  }
}

const refreshFiles = () => {
  loadFiles()
  // ElMessage.success('文件列表已刷新')
}

const beforeUpload = (file: File) => {
  const maxSize = 100 * 1024 * 1024 // 100MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过100MB')
    return false
  }
  return true
}


const formatDate = (date: string) => {
  return new Date(date).toLocaleString()
}

// 获取重复文件标签文本
const getDuplicateLabel = (duplicateType?: string) => {
  switch (duplicateType) {
    case 'name':
      return '文件名重复'
    case 'content':
      return '内容重复'
    case 'both':
      return '完全重复'
    default:
      return '重复'
  }
}

// 获取重复文件提示信息
const getDuplicateTooltip = (file: any) => {
  if (!file.isDuplicate) return ''

  let tooltip = `文件重复类型: ${getDuplicateLabel(file.duplicateType)}`

  if (file.existingFile) {
    tooltip += `\n已存在文件: ${file.existingFile.original_name}`
    tooltip += `\n上传时间: ${file.existingFile.created_at}`
  }

  return tooltip
}

// 处理重复文件操作
const handleDuplicateAction = async (command: string) => {
  switch (command) {
    case 'skip':
      // 跳过重复文件（移除重复文件）
      clearInvalidFiles()
      break

    case 'rename':
      // 自动重命名重复文件
      await autoRenameFiles()
      break

    case 'replace':
      // 替换已存在文件（标记为强制上传）
      await markForceUpload()
      break

    case 'view-details':
      // 显示重复文件详情
      showDuplicateDetails()
      break
  }
}

// 自动重命名重复文件
const autoRenameFiles = async () => {
  const duplicateFileItems = uploadFileList.value.filter(f => f.isDuplicate)

  duplicateFileItems.forEach(file => {
    const timestamp = Date.now()
    const ext = file.name.substring(file.name.lastIndexOf('.'))
    const nameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.'))

    file.name = `${nameWithoutExt}_${timestamp}${ext}`
    file.isDuplicate = false
    file.duplicateType = undefined
    file.existingFile = undefined
  })

  // 清空重复文件列表，因为已经处理了
  duplicateFiles.value = []

  ElMessage.success(`已自动重命名 ${duplicateFileItems.length} 个重复文件`)
}

// 标记强制上传（替换已存在文件）
const markForceUpload = async () => {
  try {
    const duplicateFileItems = uploadFileList.value.filter(f => f.isDuplicate)

    await ElMessageBox.confirm(
      `确定要替换已存在的 ${duplicateFileItems.length} 个文件吗？这将覆盖服务器上的同名文件。`,
      '确认替换',
      {
        confirmButtonText: '确定替换',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    duplicateFileItems.forEach(file => {
      file.forceUpload = true // 标记为强制上传
      file.isDuplicate = false // 移除重复标记以允许上传
      file.duplicateType = undefined // 清除重复类型
      file.existingFile = undefined // 清除已存在文件信息
    })

    // 清空重复文件列表，因为已经处理了
    duplicateFiles.value = []

    ElMessage.success(`已标记 ${duplicateFileItems.length} 个文件为强制上传，将替换服务器上的同名文件`)

  } catch (error) {
    // 用户取消操作
  }
}

// 显示重复文件详情
const showDuplicateDetails = () => {
  const duplicateFileItems = uploadFileList.value.filter(f => f.isDuplicate)

  // 创建HTML格式的详情内容
  let htmlContent = `
    <div style="max-height: 400px; overflow-y: auto; text-align: left;">
      <h4 style="margin-top: 0; color: #409eff;">重复文件详情 (${duplicateFileItems.length} 个)</h4>
  `

  duplicateFileItems.forEach((file, index) => {
    htmlContent += `
      <div style="border: 1px solid #e4e7ed; border-radius: 4px; padding: 12px; margin-bottom: 8px; background: #f5f7fa;">
        <div style="font-weight: bold; color: #303133; margin-bottom: 8px;">
          ${index + 1}. ${file.name}
        </div>
        <div style="font-size: 12px; color: #606266; line-height: 1.5;">
          <div><strong>重复类型:</strong> ${getDuplicateLabel(file.duplicateType)}</div>
          <div><strong>文件大小:</strong> ${formatFileSize(file.size)}</div>
    `

    if (file.existingFile) {
      htmlContent += `
          <div><strong>已存在文件:</strong> ${file.existingFile.original_name}</div>
          <div><strong>上传时间:</strong> ${file.existingFile.created_at}</div>
      `
    }

    htmlContent += `
        </div>
      </div>
    `
  })

  htmlContent += '</div>'

  ElMessageBox.alert(htmlContent, '重复文件详情', {
    confirmButtonText: '确定',
    type: 'info',
    dangerouslyUseHTMLString: true
  })
}

// 显示重复文件处理对话框
const showDuplicateHandlingDialog = async (duplicateFiles: any[]) => {
  const fileNames = duplicateFiles.map(f => f.name).join('\n')

  try {
    const { value: action } = await ElMessageBox.prompt(
      `检测到 ${duplicateFiles.length} 个重复文件：\n\n${fileNames}\n\n请选择处理方式：\n1. 重命名后重新上传\n2. 强制替换已存在文件\n3. 跳过这些文件`,
      '处理重复文件',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '输入 1、2 或 3',
        inputValidator: (value) => {
          if (!['1', '2', '3'].includes(value)) {
            return '请输入 1、2 或 3'
          }
          return true
        }
      }
    )

    switch (action) {
      case '1':
        // 重命名后重新上传
        await retryUploadWithRename(duplicateFiles)
        break
      case '2':
        // 强制替换
        await retryUploadWithForce(duplicateFiles)
        break
      case '3':
        // 跳过
        ElMessage.info('已跳过重复文件')
        break
    }
  } catch (error) {
    // 用户取消
  }
}

// 重命名后重新上传
const retryUploadWithRename = async (duplicateFiles: any[]) => {
  duplicateFiles.forEach(file => {
    const timestamp = Date.now()
    const ext = file.name.substring(file.name.lastIndexOf('.'))
    const nameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.'))

    file.name = `${nameWithoutExt}_${timestamp}${ext}`
    file.status = 'ready'
    file.errorMessage = ''
    file.isDuplicate = false
  })

  // 重新上传这些文件
  await startUpload()
}

// 强制上传（替换已存在文件）
const retryUploadWithForce = async (duplicateFiles: any[]) => {
  duplicateFiles.forEach(file => {
    file.forceUpload = true
    file.status = 'ready'
    file.errorMessage = ''
    file.isDuplicate = false
  })

  // 重新上传这些文件
  await startUpload()
}

// 切换文件选择状态
const toggleFileSelection = (file: FileItem) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file)
  }
}

// 批量删除文件
const batchDelete = async () => {
  if (selectedFiles.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 开始批量删除
    batchDeleting.value = true
    deleteProgress.value = 0
    deleteTotal.value = selectedFiles.value.length
    deleteErrors.value = []
    
    const filesToDelete = [...selectedFiles.value]
    let successCount = 0
    
    // 逐个删除文件以提供进度反馈
    for (let i = 0; i < filesToDelete.length; i++) {
      const file = filesToDelete[i]
      deletingFiles.value.add(file.id)
      
      try {
        await apiMethods.files.delete(file.id)
        successCount++
        // 移除单个文件成功提示，避免消息轰炸
      } catch (error) {
        console.error(`删除文件 ${file.original_name} 失败:`, error)
        deleteErrors.value.push(`删除文件 "${file.original_name}" 失败`)
        ElMessage.error(`删除文件 "${file.original_name}" 失败`)
      } finally {
        deletingFiles.value.delete(file.id)
        deleteProgress.value = i + 1
      }
      
      // 添加小延迟以提供更好的用户体验
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    // 显示最终结果
    if (successCount === filesToDelete.length) {
      ElMessage.success(`成功删除 ${successCount} 个文件`)
    } else if (successCount > 0) {
      ElMessage.warning(`成功删除 ${successCount} 个文件，${deleteErrors.value.length} 个文件删除失败`)
    } else {
      ElMessage.error('所有文件删除失败')
    }
    
    selectedFiles.value = []
    loadFiles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  } finally {
    batchDeleting.value = false
    deleteProgress.value = 0
    deleteTotal.value = 0
    deleteErrors.value = []
  }
}

// 文件类型过滤处理
const handleFileTypeChange = () => {
  // 当文件类型过滤改变时，重新验证已选择的文件
  if (uploadFileList.value.length > 0) {
    const beforeCount = uploadFileList.value.length
    
    // 过滤不符合新类型要求的文件
    uploadFileList.value = uploadFileList.value.filter(file => {
      return isFileTypeAllowed(file.name)
    })
    
    // 同时更新Element Plus的文件列表
    if (uploadRef.value) {
      const elementFileList = uploadRef.value.uploadFiles
      uploadRef.value.uploadFiles = elementFileList.filter((file: any) => {
        return isFileTypeAllowed(file.name)
      })
    }
    
    const afterCount = uploadFileList.value.length
    const removedCount = beforeCount - afterCount
    
    if (removedCount > 0) {
      // ElMessage.info(`已移除 ${removedCount} 个不符合类型要求的文件`)
    }
    
    // 重新检测重复文件
    if (uploadFileList.value.length > 0) {
      const duplicates = checkDuplicateFiles(uploadFileList.value)
      duplicateFiles.value = duplicates
    } else {
      duplicateFiles.value = []
    }
  }
}

// 检查文件类型是否被允许
const isFileTypeAllowed = (filename: string): boolean => {
  if (allowedFileTypes.value.length === 0 || allowedFileTypes.value.includes('other')) {
    return true
  }
  
  const ext = getFileExtension(filename)
  const typeMap: Record<string, string[]> = {
    image: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
    document: ['pdf', 'doc', 'docx', 'txt', 'md'],
    video: ['mp4', 'avi', 'mov', 'mkv', 'webm'],
    audio: ['mp3', 'wav', 'flac', 'aac', 'ogg'],
    archive: ['zip', 'rar', '7z', 'tar', 'gz']
  }
  
  return allowedFileTypes.value.some(type => {
    const extensions = typeMap[type] || []
    return extensions.includes(ext)
  })
}

// 检测重复文件
const checkDuplicateFiles = (newFiles: any[]): string[] => {
  const duplicates: string[] = []
  const existingNames = files.value.map(f => f.original_name)
  const fileNameCounts = new Map<string, number>()
  
  // 统计文件名出现次数
  newFiles.forEach(file => {
    const count = fileNameCounts.get(file.name) || 0
    fileNameCounts.set(file.name, count + 1)
  })
  
  // 检查重复文件
  newFiles.forEach(file => {
    const fileName = file.name
    
    // 检查是否与服务器上已有文件重复
    if (existingNames.includes(fileName) && !duplicates.includes(fileName)) {
      duplicates.push(fileName)
    }
    
    // 检查是否在当前选择的文件中有重复（出现次数大于1）
    if ((fileNameCounts.get(fileName) || 0) > 1 && !duplicates.includes(fileName)) {
      duplicates.push(fileName)
    }
  })
  
  return duplicates
}

// 处理文件变更的核心逻辑
const processFileChange = (file: any, fileList: any[]) => {
  // 文件数量提示（移除硬性限制，改为友好提示）
  if (fileList.length > 1000) {
    // ElMessage.info(`您选择了 ${fileList.length }个文件，系统将分批处理以确保最佳性能`)
  }
  
  // 收集所有错误信息，避免多次弹窗
  const errors: string[] = []
  const invalidFiles: string[] = []
  
  // 过滤不允许的文件类型
  const validFiles = fileList.filter(item => {
    if (!isFileTypeAllowed(item.name)) {
      invalidFiles.push(item.name)
      return false
    }
    return true
  })
  
  // 如果有无效文件，统一显示一次警告
  if (invalidFiles.length > 0) {
    if (invalidFiles.length === 1) {
      errors.push(`文件 "${invalidFiles[0]}" 类型不被允许`)
    } else {
      errors.push(`${invalidFiles.length}个文件类型不被允许`)
    }
  }
  
  // 当文件列表改变时更新我们的文件列表
  uploadFileList.value = validFiles.map(item => ({
    name: item.name,
    size: item.size,
    status: 'ready',
    errorMessage: '',
    file: item.raw,
    isDuplicate: false, // 初始设为false，后面会更新
    duplicateType: undefined, // 重复类型：'name', 'content', 'both'
    existingFile: undefined, // 已存在的文件信息
    forceUpload: false // 是否强制上传（替换已存在文件）
  }))
  
  // 更新重复文件状态（这会设置isDuplicate标记）
  updateDuplicateFiles()
  
  // 检测重复文件数量并提示
  const duplicateCount = uploadFileList.value.filter(f => f.isDuplicate).length
  if (duplicateCount > 0) {
    if (duplicateCount === 1) {
      errors.push(`检测到 1个重复文件`)
    } else {
      errors.push(`检测到 ${duplicateCount}个重复文件`)
    }
  }
  
  // 统一显示所有错误信息
  if (errors.length > 0) {
    ElMessage.warning(errors.join('；'))
  }
  
  // 重置分页
  fileListPage.value = 1
}

// 切换文件列表视图
const toggleFileListView = () => {
  showAllFiles.value = !showAllFiles.value
  if (showAllFiles.value) {
    fileListPage.value = 1 // 展开时重置到第一页
  }
}

// 防抖版本的文件处理函数
const debouncedProcessFileChange = debounce(processFileChange, 300)

// 新的上传相关方法
const handleFileChange = (file: any, fileList: any[]) => {
  // 防止重复处理相同的文件列表
  const currentFileNames = fileList.map(f => f.name).sort().join(',')
  const existingFileNames = uploadFileList.value.map(f => f.name).sort().join(',')
  
  if (currentFileNames === existingFileNames) {
    return
  }
  
  // 使用防抖处理
  debouncedProcessFileChange(file, fileList)
}

const clearFileList = () => {
  const fileCount = uploadFileList.value.length
  
  uploadFileList.value = []
  duplicateFiles.value = []
  showAllFiles.value = false
  fileListPage.value = 1 // 重置页码
  
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  if (fileCount > 0) {
    // ElMessage.info(`已清空 ${fileCount}个文件`)
  }
}

const startUpload = async () => {
  if (uploadFileList.value.length === 0) return
  
  // 只上传非重复文件
  const filesToUpload = uploadFileList.value.filter(file => !file.isDuplicate)
  
  if (filesToUpload.length === 0) {
    ElMessage.warning('没有可上传的文件，所有文件都是重复的')
    return
  }
  
  isUploading.value = true
  uploadStartTime.value = Date.now()
  uploadedBytes.value = 0
  
  try {
    // 逐个上传文件
    for (let i = 0; i < filesToUpload.length; i++) {
      const fileItem = filesToUpload[i]
      currentUploadingFile.value = fileItem.name
      fileItem.status = 'uploading'
      
      await uploadSingleFile(fileItem, i)
    }
    
    // 所有文件上传完成
    const successCount = filesToUpload.filter(f => f.status === 'success').length
    const errorCount = filesToUpload.filter(f => f.status === 'error').length
    
    if (successCount > 0) {
      // ElMessage.success(`成功上传 ${successCount}个文件${errorCount > 0 ? `，${errorCount}个文件失败` : ''}`)
      
      // 移除已成功上传的文件
      uploadFileList.value = uploadFileList.value.filter(f => f.status !== 'success')
      
      // 更新Element Plus的文件列表
      if (uploadRef.value && uploadRef.value.uploadFiles) {
        const remainingFileNames = uploadFileList.value.map(f => f.name)
        uploadRef.value.uploadFiles = uploadRef.value.uploadFiles.filter((file: any) => {
          return remainingFileNames.includes(file.name)
        })
      }
      
      // 重新检查重复文件
      updateDuplicateFiles()
      
      loadFiles() // 刷新文件列表
    }
    
    if (errorCount === filesToUpload.length) {
      // 检查是否都是重复文件错误
      const duplicateErrors = uploadFileList.value.filter(f =>
        f.status === 'error' && f.errorMessage &&
        (f.errorMessage.includes('重复') || f.errorMessage.includes('已存在'))
      )

      if (duplicateErrors.length > 0) {
        // 显示重复文件处理对话框
        showDuplicateHandlingDialog(duplicateErrors)
      } else {
        ElMessage.error('所有文件上传失败')
      }
    }
    
  } catch (error) {
    console.error('上传过程中出错:', error)
    ElMessage.error('上传过程中出现错误')
  } finally {
    isUploading.value = false
    currentUploadingFile.value = ''
  }
}

const uploadSingleFile = (fileItem: any, index: number): Promise<void> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('files', fileItem.file)

    // 如果是强制上传，添加参数
    if (fileItem.forceUpload) {
      formData.append('force_upload', 'true')
    }

    const xhr = new XMLHttpRequest()
    
    // 上传进度监听 - 只更新整体进度，不更新单个文件进度
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        // 更新总体进度
        updateOverallProgress()
      }
    })
    
    // 上传完成监听
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText)
          if (response.success) {
            fileItem.status = 'success'
          } else {
            fileItem.status = 'error'
            fileItem.errorMessage = response.message || '上传失败'

            // 如果是重复文件错误，标记为重复并提供处理选项
            if (response.message && (response.message.includes('重复') || response.message.includes('已存在'))) {
              fileItem.isDuplicate = true
              fileItem.duplicateType = response.message.includes('内容重复') ? 'content' : 'name'
            }
          }
        } catch (error) {
          fileItem.status = 'error'
          fileItem.errorMessage = '响应解析失败'
        }
      } else {
        fileItem.status = 'error'
        fileItem.errorMessage = `HTTP ${xhr.status}`
      }
      resolve()
    })
    
    // 上传错误监听
    xhr.addEventListener('error', () => {
      fileItem.status = 'error'
      fileItem.errorMessage = '网络错误'
      resolve()
    })
    
    // 发送请求
    xhr.open('POST', uploadUrl.value)
    xhr.setRequestHeader('Authorization', uploadHeaders.value.Authorization)
    xhr.send(formData)
  })
}

const updateOverallProgress = () => {
  // 计算平均速度和剩余时间
  const elapsed = (Date.now() - uploadStartTime.value) / 1000
  const completedFiles = uploadFileList.value.filter(f => f.status === 'success').length
  const remainingFiles = uploadFileList.value.length - completedFiles
  
  if (elapsed > 0 && completedFiles > 0) {
    const avgTimePerFile = elapsed / completedFiles
    const estimatedRemainingTime = avgTimePerFile * remainingFiles
    estimatedTime.value = formatTime(estimatedRemainingTime)
    
    // 计算总体速度 - 基于已完成文件的总大小
    const completedBytes = uploadFileList.value
      .filter(f => f.status === 'success')
      .reduce((sum, file) => sum + file.size, 0)
    const speed = completedBytes / elapsed
    averageSpeed.value = formatSpeed(speed)
  }
}

const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond < 1024) return `${Math.round(bytesPerSecond)} B/s`
  if (bytesPerSecond < 1024 * 1024) return `${Math.round(bytesPerSecond / 1024)} KB/s`
  return `${Math.round(bytesPerSecond / (1024 * 1024))} MB/s`
}

const formatTime = (seconds: number): string => {
  if (seconds < 60) return `${Math.round(seconds)}秒`
  if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`
  return `${Math.round(seconds / 3600)}小时`
}

const cancelUpload = () => {
  isUploading.value = false
  currentUploadingFile.value = ''
  
  // 将正在上传的文件标记为取消
  uploadFileList.value.forEach(file => {
    if (file.status === 'uploading') {
      file.status = 'error'
      file.errorMessage = '用户取消'
    }
  })
  
  // ElMessage.info('上传已取消')
}

const finishUpload = () => {
  showUploadDialog.value = false
  uploadFileList.value = []
  duplicateFiles.value = []
  showAllFiles.value = false
  isUploading.value = false
  currentUploadingFile.value = ''
  
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  // ElMessage.success('上传完成')
}

// 重写原有的上传处理方法
const handleUploadSuccess = (response: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

const handleUploadError = (error: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

// 上传进度处理
const handleUploadProgress = (event: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

// 批量搜索相关方法
const handleBatchSearchInput = () => {
  // 处理批量搜索输入，按行分割关键词
  const keywords = batchSearchText.value
    .split('\n')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)
  
  batchSearchKeywords.value = keywords
}

const clearBatchSearch = () => {
  batchSearchText.value = ''
  batchSearchKeywords.value = []
}

const executeBatchSearch = async () => {
  if (batchSearchKeywords.value.length === 0) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  if (batchSearchKeywords.value.length > 100) {
    ElMessage.error('关键词数量不能超过100个')
    return
  }
  
  try {
    loading.value = true
    showBatchSearchDialog.value = false
    
    // 修改后端API调用，支持批量搜索
    const response = await apiMethods.files.batchSearch({
      keywords: batchSearchKeywords.value,
      page: 1,
      limit: pageSize.value
    })
    
    files.value = response.data.data.files
    total.value = response.data.data.pagination.total
    currentPage.value = 1
    hasMoreFiles.value = files.value.length < total.value
    
    // 将批量搜索关键词合并为单个搜索查询以便显示
    searchQuery.value = `批量搜索(${batchSearchKeywords.value.length}个关键词)`
    
    // ElMessage.success(`找到 ${files.value.length} 个匹配的文件`)
    
  } catch (error) {
    console.error('批量搜索失败:', error)
    ElMessage.error('批量搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

// 获取文件图标颜色
const getFileIconColor = (extension: string) => {
  const colorMap: Record<string, string> = {
    pdf: '#ff4d4f',
    doc: '#1890ff',
    docx: '#1890ff',
    xls: '#52c41a',
    xlsx: '#52c41a',
    ppt: '#fa8c16',
    pptx: '#fa8c16',
    txt: '#8c8c8c',
    jpg: '#722ed1',
    jpeg: '#722ed1',
    png: '#722ed1',
    gif: '#722ed1',
    bmp: '#722ed1',
    svg: '#722ed1',
    mp4: '#eb2f96',
    avi: '#eb2f96',
    mov: '#eb2f96',
    wmv: '#eb2f96',
    mp3: '#13c2c2',
    wav: '#13c2c2',
    flac: '#13c2c2',
    zip: '#faad14',
    rar: '#faad14',
    '7z': '#faad14',
    tar: '#faad14',
    gz: '#faad14'
  }
  return colorMap[extension.toLowerCase()] || '#8c8c8c'
}

// 获取相对时间
const getRelativeTime = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// 表格行样式
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  return rowIndex % 2 === 0 ? '' : 'stripe-row'
}

// 处理文件命令
const handleFileCommand = (command: string, file: any) => {
  switch (command) {
    case 'download':
      downloadFile(file)
      break
    case 'rename':
      // TODO: 实现重命名功能
      // ElMessage.info('重命名功能开发中')
      break
    case 'copy':
      // TODO: 实现复制功能
      // ElMessage.info('复制功能开发中')
      break
    case 'move':
      // TODO: 实现移动功能
      // ElMessage.info('移动功能开发中')
      break
    case 'delete':
      handleDelete(file)
      break
  }
}

// 处理删除操作
const handleDelete = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.original_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiMethods.files.delete(file.id)
    // ElMessage.success('文件删除成功')
    loadFiles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

onMounted(() => {
  loadFiles()
  
  // 强制设置标题样式 - 使用多种选择器确保找到元素
  nextTick(() => {
    const selectors = [
      '.file-manager-banner .banner-title',
      '.file-manager-page .banner-title',
      '.file-manager-page h1',
      '.file-manager-banner h1',
      '.title-content h1',
      'h1.banner-title',
      'h1'
    ]
    
    let titleElement: HTMLElement | null = null
    
    // 尝试多个选择器找到标题元素
    for (const selector of selectors) {
      titleElement = document.querySelector(selector) as HTMLElement
      if (titleElement && titleElement.textContent?.includes('文件管理中心')) {
        break
      }
    }
    
    if (titleElement) {
      // 强制设置所有可能影响字体大小的CSS属性
      const styles = {
        'font-size': '1.25rem',
        'font-weight': '600',
        'line-height': '1.2',
        'margin': '0',
        'margin-top': '0',
        'margin-bottom': '0',
        'margin-left': '0',
        'margin-right': '0',
        'padding': '0',
        'padding-top': '0',
        'padding-bottom': '0',
        'padding-left': '0',
        'padding-right': '0',
        'letter-spacing': '-0.025em',
        'color': '#1e293b',
        'border': 'none',
        'background': 'none',
        'text-decoration': 'none',
        'outline': 'none',
        'box-shadow': 'none',
        'transform': 'none',
        'zoom': '1',
        'scale': '1'
      }
      
      // 设置每个样式属性
      Object.entries(styles).forEach(([property, value]) => {
        titleElement!.style.setProperty(property, value, 'important')
      })
      
      // 确保类名正确
      titleElement.className = 'banner-title'
      
      console.log('文件管理中心标题样式已强制设置:', titleElement.style.fontSize)
    } else {
      console.warn('未找到文件管理中心标题元素')
    }
  })
  
  // 延迟再次检查，确保样式生效
  setTimeout(() => {
    const titleElement = document.querySelector('.file-manager-banner .banner-title') as HTMLElement
    if (titleElement) {
      titleElement.style.setProperty('font-size', '1.25rem', 'important')
      console.log('延迟检查 - 文件管理中心标题样式:', titleElement.style.fontSize)
    }
  }, 100)
})
</script>

<style scoped>
.file-manager-page {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 文件管理横幅 - 简约现代风格 */
.file-manager-banner {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.file-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #64748b 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #64748b 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-text {
  flex: 1;
}

.file-manager-banner .banner-title,
.file-manager-page .banner-title,
h1.banner-title,
.file-manager-banner h1,
.file-manager-page h1,
.banner-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 8px 0 !important;
  color: #1e293b !important;
  letter-spacing: -0.025em !important;
  line-height: 1.2 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
  transform: none !important;
  zoom: 1 !important;
  scale: 1 !important;
}

.banner-subtitle {
  font-size: 0.95rem;
  margin: 0 0 16px 0;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.banner-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.upload-main-btn {
  position: relative;
  padding: 0 24px !important;
  height: 44px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.upload-main-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25) !important;
}

.upload-main-btn span {
  display: inline-block;
  transition: all 0.3s ease;
}

.upload-hint {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-left: 8px;
  font-weight: 400;
}

.upload-main-btn:hover .upload-hint {
  opacity: 1;
}

.banner-visual {
  position: relative;
  width: 80px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-manager-icon {
  width: 48px;
  height: 48px;
  background: #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.file-manager-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* 仅在支持hover的设备上启用脉冲动画 */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .file-manager-banner:hover .file-manager-icon {
    animation: iconPulse 3s ease-in-out infinite;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

/* 区块标题 - 简约现代风格 */
.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 28px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  letter-spacing: -0.025em;
}

.section-title .el-icon {
  color: #3b82f6;
  font-size: 1.2rem;
}

/* 统计卡片 - 紧凑现代风格 */
.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 10px;
  max-width: 100%;
}

.stats-card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  padding: 8px 10px !important;
  position: relative !important;
  overflow: visible !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  min-height: 60px !important;
  border-left: 3px solid transparent !important;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #e5e7eb;
}

.stats-card.primary {
  border-left-color: #3b82f6;
}

.stats-card.success {
  border-left-color: #10b981;
}

.stats-card.warning {
  border-left-color: #f59e0b;
}

.stats-card.info {
  border-left-color: #06b6d4;
}

.stats-card.primary:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stats-card.success:hover {
  border-left-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.stats-card.warning:hover {
  border-left-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.stats-card.info:hover {
  border-left-color: #06b6d4;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}


.stats-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
}

.stats-card.primary .stats-icon {
  background: #3b82f6;
}

.stats-card.success .stats-icon {
  background: #10b981;
}

.stats-card.warning .stats-icon {
  background: #f59e0b;
}

.stats-card.info .stats-icon {
  background: #06b6d4;
}

.stats-card:hover .stats-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.stats-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.stats-content {
  flex: 1;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.stats-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0;
  line-height: 1.1;
}

.stats-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 1px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.625rem;
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.8;
}

.stats-trend .el-icon {
  font-size: 0.625rem;
}

/* 快捷操作 - 现代化卡片设计 */
.quick-actions-section {
  margin-bottom: 32px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.quick-action-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 80px;
  position: relative;
  overflow: hidden;
}

.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.quick-action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: rgba(59, 130, 246, 0.3);
}

.quick-action-card:hover::before {
  opacity: 1;
}

.action-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
}

.quick-action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.action-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
  z-index: 1;
}

.action-content h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.action-content p {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
  opacity: 0.9;
}

.action-arrow {
  color: #94a3b8;
  transition: all 0.4s ease;
  font-size: 1.125rem;
  position: relative;
  z-index: 1;
}

.quick-action-card:hover .action-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

/* 特定卡片的图标颜色 - 与统计卡片保持一致 */
.quick-action-card:nth-child(1) .action-icon {
  background: #3b82f6;
}

.quick-action-card:nth-child(2) .action-icon {
  background: #10b981;
}

.quick-action-card:nth-child(3) .action-icon {
  background: #f59e0b;
}

.quick-action-card:nth-child(4) .action-icon {
  background: #06b6d4;
}

.quick-action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* 文件浏览器 - 简约现代风格 */
.file-browser-section {
  margin-bottom: 48px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
}

.section-header h2 {
  margin: 0;
}

.browser-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.browser-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 批量操作栏 - 简约现代风格 */
.batch-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 28px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.batch-actions {
  display: flex;
  gap: 12px;
}

/* 表格容器 - 简约现代风格 */
.table-container {
  padding: 0;
  max-height: 600px;
  overflow-y: auto;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

/* 美化表格样式 */
.table-container :deep(.el-table) {
  font-size: 14px;
}

.table-container :deep(.el-table__header-wrapper) {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.table-container :deep(.el-table__header th) {
  background: #f8fafc !important;
  color: #475569;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: 16px 0;
}

.table-container :deep(.el-table__body-wrapper) {
  background: white;
}

.table-container :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.table-container :deep(.el-table__row:hover) {
  background: #f8fafc !important;
}

.table-container :deep(.el-table__row.stripe-row) {
  background: #fafbfc;
}

.table-container :deep(.el-table__row.stripe-row:hover) {
  background: #f1f5f9 !important;
}

/* 文件信息增强样式 */
.file-info-enhanced {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.file-icon-box {
  position: relative;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.table-container :deep(.el-table__row:hover) .file-icon-box {
  background: #e2e8f0;
  transform: scale(1.05);
}

.file-ext-badge {
  position: absolute;
  bottom: -4px;
  right: -4px;
  padding: 2px 6px;
  background: #3b82f6;
  color: white;
  font-size: 0.625rem;
  font-weight: 700;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name-text {
  display: block;
  font-weight: 500;
  color: #1e293b;
  font-size: 0.95rem;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta-info {
  display: block;
  font-size: 0.75rem;
  color: #64748b;
}

/* 文件大小样式 */
.size-text {
  font-weight: 500;
  color: #475569;
  font-size: 0.875rem;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-date {
  font-weight: 500;
  color: #475569;
  font-size: 0.875rem;
}

.time-clock {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-actions .el-button {
  transition: all 0.2s ease;
}

.table-actions .el-button:hover {
  transform: translateX(2px);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: left; /* 确保文件名左对齐 */
}

.file-info span {
  text-align: left; /* 确保文件名文本左对齐 */
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

/* 网格视图 - 简约现代风格 */
.file-grid-container {
  padding: 28px;
  max-height: 600px;
  overflow-y: auto;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.empty-state {
  padding: 48px 24px;
  text-align: center;
}

.empty-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-tertiary);
}

.empty-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.empty-content p {
  margin: 0 0 24px 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
}

.file-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.file-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.file-item.selected {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.04);
}

.file-item-icon {
  margin-bottom: 16px;
}

.file-item-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
  word-break: break-word;
  line-height: 1.4;
  text-align: left;
  font-size: 0.9rem;
}

.file-item-info {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  font-size: 0.875rem;
  gap: 8px;
}

.file-size {
  font-weight: 500;
  color: #64748b;
}

.file-item-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 分页 - 简约现代风格 */
.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 28px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  gap: 24px;
}

/* 加载更多 */
.load-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.load-more-btn {
  min-width: 200px;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.load-more-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

/* 上传对话框 */
.upload-dialog-content {
  padding: 16px;
  max-height: 600px;
  overflow-y: auto;
}

.upload-area {
  border-radius: 12px;
  overflow: visible;
}

.modern-upload {
  border-radius: 12px;
}

.modern-upload :deep(.el-upload-dragger) {
  padding: 20px 20px 10px 20px;
  height: auto;
  min-height: 160px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
  min-height: 120px;
}

.upload-animation {
  position: relative;
  margin-bottom: 16px;
}

.upload-icon {
  font-size: 36px;
  color: var(--el-color-primary);
  opacity: 0.8;
  position: relative;
  z-index: 2;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.upload-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 2px solid var(--el-color-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: ripple 2s ease-out infinite;
}

@keyframes ripple {
  0% {
    width: 30px;
    height: 30px;
    opacity: 0.6;
  }
  100% {
    width: 90px;
    height: 90px;
    opacity: 0;
  }
}

.upload-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.upload-subtitle {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.upload-subtitle em {
  color: var(--el-color-primary);
  font-style: normal;
  font-weight: 500;
}

.upload-formats {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 8px;
  padding: 4px 12px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.upload-tips {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 12px;
  padding: 0 20px 10px 20px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.3;
}

.tip-item .el-icon {
  margin-right: 8px;
}

.tip-item.success {
  color: var(--el-color-success);
}

.tip-item.success .el-icon {
  color: var(--el-color-success);
}

.tip-item.warning {
  color: var(--el-color-warning);
}

.tip-item.warning .el-icon {
  color: var(--el-color-warning);
}

.tip-item.info {
  color: var(--el-color-info);
}

.tip-item.info .el-icon {
  color: var(--el-color-info);
}

.tip-item.primary {
  color: var(--el-color-primary);
  font-weight: 600;
}

.tip-item.primary .el-icon {
  color: var(--el-color-primary);
}

.tip-item-plain {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
  margin: 4px 0;
  line-height: 1.3;
}

.tip-item-plain .el-icon {
  color: #64748b;
  margin-right: 8px;
}

.tip-item-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 400;
  margin: 8px 0;
  background: none;
  border: none;
  padding: 0;
}

.tip-item-text .el-icon {
  color: #64748b;
  margin-right: 8px;
}

/* 紧凑文件列表样式 */
.compact-file-list {
  width: 100%;
}

.compact-file-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.compact-file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.compact-file-item:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.02);
  transform: translateX(4px);
}

.compact-file-item.is-duplicate {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.02);
}

.compact-file-icon {
  font-size: 1.25rem;
  color: #64748b;
  flex-shrink: 0;
}

.compact-file-name {
  flex: 1;
  font-weight: 500;
  color: #1e293b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.compact-file-size {
  font-size: 0.8rem;
  color: #64748b;
  flex-shrink: 0;
  margin-right: 8px;
}

.compact-file-item .el-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.compact-file-item:hover .el-button {
  opacity: 1;
}

/* 文件列表滚动样式 */
.expanded-file-view {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.file-count-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
}

.file-count-info .el-icon {
  color: #3b82f6;
}

.scrollable-file-list {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fafbfc;
  padding: 8px;
  
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.scrollable-file-list::-webkit-scrollbar {
  width: 8px;
}

.scrollable-file-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.scrollable-file-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s;
}

.scrollable-file-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 优化文件卡片在滚动容器中的样式 */
.scrollable-file-list .modern-file-card {
  margin-bottom: 8px;
}

.scrollable-file-list .modern-file-card:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-manager-page {
    padding: 16px;
  }
  
  .welcome-banner {
    padding: 24px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .banner-title {
    font-size: 1.25rem !important;
  }
  
  .welcome-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stats-card {
    padding: 10px;
    gap: 8px;
    min-height: 55px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .stats-icon .el-icon {
    font-size: 0.9rem !important;
    width: 0.9rem !important;
    height: 0.9rem !important;
  }

  .stats-value {
    font-size: 1.3rem;
  }

  .stats-label {
    font-size: 0.75rem;
  }

  .stats-trend {
    font-size: 0.65rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .quick-action-card {
    padding: 16px;
    gap: 12px;
    min-height: 70px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
    border-radius: 10px;
  }

  .action-content h3 {
    font-size: 0.9rem;
  }

  .action-content p {
    font-size: 0.8rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .browser-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .banner-title {
    font-size: 1.25rem !important;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .stats-card {
    padding: 8px;
    gap: 6px;
    min-height: 50px;
  }

  .stats-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .stats-icon .el-icon {
    font-size: 0.8rem !important;
    width: 0.8rem !important;
    height: 0.8rem !important;
  }

  .stats-value {
    font-size: 1.1rem;
  }

  .stats-label {
    font-size: 0.7rem;
  }

  .stats-trend {
    font-size: 0.6rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .quick-action-card {
    padding: 14px;
    gap: 10px;
    min-height: 60px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
    border-radius: 8px;
  }

  .action-content h3 {
    font-size: 0.85rem;
  }

  .action-content p {
    font-size: 0.75rem;
  }
   
  .welcome-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .welcome-actions .el-button {
    width: 100%;
  }
  
  .stats-card {
    padding: 16px;
  }
  
  .quick-action-card {
    padding: 8px;
    gap: 8px;
    min-height: 50px;
  }

  .action-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .action-content h3 {
    font-size: 0.8rem;
  }

  .action-content p {
    font-size: 0.65rem;
  }
  
  .file-grid {
    grid-template-columns: 1fr;
  }
}

/* 上传指南样式 */
.upload-guide-content {
  padding: 20px 0;
}

.guide-section {
  margin-bottom: 32px;
}

.guide-section:last-child {
  margin-bottom: 0;
}

.guide-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.guide-section h3 .el-icon {
  color: var(--el-color-primary);
}

.file-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.file-type-item {
  text-align: center;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.file-type-item:hover {
  border-color: var(--el-color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-type-item .el-icon {
  font-size: 32px;
  color: var(--el-color-primary);
  margin-bottom: 8px;
  display: block;
}

.file-type-item span {
  display: block;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.file-type-item small {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.guide-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.guide-list li {
  padding: 8px 0;
  padding-left: 24px;
  position: relative;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.guide-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--el-color-success);
  font-weight: bold;
}

.guide-list.warning li::before {
  content: '⚠';
  color: var(--el-color-warning);
}

/* 上传进度样式 */
.upload-progress-section {
  margin-top: 24px;
  padding: 20px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
}

.progress-header {
  margin-bottom: 20px;
}

.progress-header h4 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.overall-progress {
  background: var(--bg-card);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.file-progress-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 16px;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.file-progress-list.collapsed {
  max-height: 200px;
}

/* 文件列表控制区域 */
.file-list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.file-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-item.success {
  color: #22c55e;
}

.stat-item.error {
  color: #ef4444;
}

.stat-item.uploading {
  color: var(--primary);
}

.list-toggle {
  flex-shrink: 0;
}

/* 折叠提示 */
.collapsed-hint {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  margin-top: 8px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px dashed var(--border-primary);
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.hint-content .el-icon {
  color: var(--text-tertiary);
}

.file-progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.file-progress-item.uploading {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.file-progress-item.success {
  border-color: #22c55e;
  background: rgba(34, 197, 94, 0.05);
}

.file-progress-item.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.file-progress-item .file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 1rem;
}

.file-progress-item.success .file-icon {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.file-progress-item.error .file-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.file-progress-item.uploading .file-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-progress-item .file-size {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.file-status {
  flex: 0 0 120px;
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 12px;
  text-align: center;
}

.status-text.success {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.status-text.uploading {
  color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.error-message {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 4px;
  text-align: right;
}

.current-status {
  padding: 16px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.status-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-stats {
  display: flex;
  gap: 24px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

/* 统一的头部样式 */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 文件操作区域样式 */
.file-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 200px;
}

.file-progress-item.ready {
  border-color: var(--border-primary);
  background: var(--bg-card);
}

.file-progress-item.ready:hover {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.file-progress-item.ready .file-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

/* 批量搜索对话框样式 */
.batch-search-content {
  padding: 16px;
}

.search-instructions {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.search-instructions h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.search-instructions ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.search-instructions li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.search-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 6px;
  font-size: 0.875rem;
}

.error-text {
  color: #ef4444;
  font-weight: 500;
}

.keywords-preview {
  margin-top: 16px;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.keywords-preview h5 {
  margin: 0 0 12px 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}
/* 上传中视图样式 */
.uploading-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overall-progress {
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.files-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.main-progress {
  margin-top: 8px;
}

.current-status {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.current-file {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
}

.upload-stats {
  display: flex;
  gap: 24px;
  font-size: 0.875rem;
  color: #64748b;
}

.upload-summary {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.summary-item.success {
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.2);
  background: rgba(34, 197, 94, 0.05);
}

.summary-item.error {
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(239, 68, 68, 0.05);
}

.summary-item.uploading {
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.2);
  background: rgba(59, 130, 246, 0.05);
}

.file-selection-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .uploading-view {
    gap: 16px;
  }
  
  .overall-progress {
    padding: 20px;
  }
  
  .progress-text {
    font-size: 1rem;
  }
  
  .current-status {
    padding: 16px;
  }
  
  .upload-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .upload-summary {
    gap: 12px;
  }
  
  .summary-item {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .overall-progress {
    padding: 16px;
  }
  
  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .progress-text {
    font-size: 0.95rem;
  }
  
  .files-count {
    font-size: 0.8rem;
  }
  
  .current-file {
    font-size: 0.9rem;
  }
  
  .upload-summary {
    flex-direction: column;
    gap: 8px;
  }
  
  .summary-item {
    justify-content: center;
  }
}

/* 新增的优化样式 */
.file-progress-item.duplicate {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.file-progress-item.duplicate .file-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.file-progress-item.ready .file-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.file-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.file-type {
  font-size: 0.7rem;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.list-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 上传过滤器样式 */
.upload-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.filter-section .el-checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.filter-section .el-checkbox {
  margin-right: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .filter-section .el-checkbox-group {
    grid-template-columns: 1fr;
  }
  
  .file-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .list-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}

/* 现代化上传样式 */
.modern-upload-section {
  margin-top: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.upload-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  min-height: 72px;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.025em;
  white-space: nowrap;
}

.header-title .el-icon {
  font-size: 1.25rem;
  color: #3b82f6;
}

.header-stats {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  margin-left: 0;
  margin-top: 0;
}

.stat-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  background: #f1f5f9;
  border-radius: 16px;
  font-size: 0.8125rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
  white-space: nowrap;
  height: 28px;
}

.stat-badge span {
  display: inline-block;
  white-space: nowrap;
  line-height: 1;
}

.stat-badge .el-icon {
  font-size: 0.875rem;
  line-height: 1;
}

.stat-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-badge.total {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.stat-badge.size {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.stat-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
}

.header-actions .el-button {
  font-weight: 600;
  transition: all 0.3s ease;
}

.header-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.upload-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 8px;
  margin-left: 8px;
  background: white;
  color: #3b82f6;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 700;
}

/* 现代化文件列表 */
.modern-file-list {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  max-height: 400px;
}

.quick-actions-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.modern-file-grid {
  position: relative;
  transition: max-height 0.3s ease;
}

.modern-file-grid.collapsed {
  max-height: 400px;
  overflow: hidden;
}

.file-list-container {
  display: grid;
  gap: 12px;
}

/* 现代化文件卡片 */
.modern-file-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-file-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-file-card:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.02);
  transform: translateX(4px);
}

.modern-file-card:hover::before {
  opacity: 1;
}

.modern-file-card.is-duplicate {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.02);
}

/* 内容重复文件样式（更严重的警告） */
.modern-file-card.is-duplicate[data-duplicate-type="content"] {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.02);
}

.modern-file-card.is-duplicate[data-duplicate-type="both"] {
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

/* 旋转动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotating {
  animation: rotate 1s linear infinite;
}

.modern-file-card.is-large {
  border-style: dashed;
}

/* 文件图标样式 */
.file-icon-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.file-icon-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-radius: 8px;
  opacity: 0.5;
}

.file-main-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
  color: #4f46e5;
  z-index: 1;
}

.file-ext {
  position: absolute;
  bottom: -2px;
  right: -2px;
  padding: 1px 4px;
  background: #4f46e5;
  color: white;
  font-size: 0.5rem;
  font-weight: 700;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 文件内容样式 */
.file-content {
  flex: 1;
  min-width: 0;
  z-index: 1;
}

.file-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.file-name {
  font-weight: 500;
  color: #1e293b;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.file-meta-row {
  display: flex;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #64748b;
}

.meta-item .el-icon {
  font-size: 0.75rem;
  opacity: 0.7;
}

/* 文件操作样式 */
.file-action {
  z-index: 1;
}

/* 折叠提示样式 */
.modern-collapsed-hint {
  position: relative;
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  text-align: center;
}

.hint-decoration {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #cbd5e1, transparent);
}

.hint-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.hint-icon {
  font-size: 1.5rem;
  color: #94a3b8;
}

.hint-text {
  font-size: 0.95rem;
  color: #64748b;
}

.hint-text strong {
  color: #3b82f6;
  font-weight: 700;
}

/* 动画效果 */
.file-list-enter-active,
.file-list-leave-active {
  transition: all 0.3s ease;
}

.file-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.file-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-header {
    padding: 16px;
    min-height: auto;
  }
  
  .header-top {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .header-title {
    font-size: 1rem;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .header-actions .el-button {
    flex: 1;
  }
  
  .modern-file-card {
    padding: 12px 16px;
  }
  
  .file-icon-wrapper {
    width: 48px;
    height: 48px;
  }
  
  .file-name {
    max-width: 200px;
  }
  
  .file-meta-row {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .header-stats {
    gap: 6px;
    flex-wrap: wrap;
    margin-top: 8px;
  }
  
  .stat-badge {
    padding: 4px 8px;
    font-size: 0.75rem;
  }
  
  .modern-file-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }
  
  .file-content {
    width: 100%;
  }
  
  .file-name-row {
    justify-content: center;
  }
  
  .file-meta-row {
    justify-content: center;
  }
}

/* 批量删除进度样式 */
.batch-delete-progress {
  margin: 16px 0;
  padding: 20px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.batch-delete-progress .progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.batch-delete-progress .progress-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-delete-progress .progress-text {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.delete-progress-bar {
  margin-bottom: 16px;
}

.delete-errors {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
}

.delete-errors .el-alert {
  margin: 0;
}

/* 删除状态的行样式 */
.table-container :deep(.el-table__row.deleting) {
  background: rgba(239, 68, 68, 0.05) !important;
  opacity: 0.7;
}

.table-container :deep(.el-table__row.deleting:hover) {
  background: rgba(239, 68, 68, 0.1) !important;
}

/* 网格视图中删除状态的文件项样式 */
.file-item.deleting {
  opacity: 0.7;
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  pointer-events: none;
}

.file-item.deleting .file-item-name {
  color: #64748b;
}

/* 旋转动画 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-delete-progress {
    padding: 16px;
    margin: 12px 0;
  }
  
  .batch-delete-progress .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .delete-errors {
    max-height: 80px;
  }
}

@media (max-width: 480px) {
  .batch-delete-progress {
    padding: 12px;
  }
  
  .batch-delete-progress .progress-header h4 {
    font-size: 0.9rem;
  }
  
  .batch-delete-progress .progress-text {
    font-size: 0.8rem;
  }
}
</style>