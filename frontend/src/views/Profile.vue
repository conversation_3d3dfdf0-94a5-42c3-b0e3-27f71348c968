<template>
  <div class="profile-page">
    <!-- 个人设置横幅 -->
    <div class="profile-banner">
      <div class="banner-background">
        <div class="file-pattern"></div>
      </div>
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">个人设置</h1>
          <p class="banner-subtitle">管理您的账户信息、安全设置和系统偏好</p>
        </div>
        <div class="banner-visual">
          <div class="profile-avatar">
            <div class="avatar-circle">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="avatar-info">
              <div class="avatar-name">{{ profileForm.username }}</div>
              <div class="avatar-role" :class="roleClass">
                <el-icon><Star v-if="isAdmin" /><User v-else /></el-icon>
                {{ roleText }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        账户统计
      </h2>
      <div class="stats-overview">
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.file_count || 0 }}</div>
            <div class="stat-label">管理文件</div>
            <div class="stat-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>已上传文件</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card success">
          <div class="stat-icon">
            <el-icon><Folder /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.directory_count || 0 }}</div>
            <div class="stat-label">文件夹</div>
            <div class="stat-trend">
              <el-icon><FolderOpened /></el-icon>
              <span>目录数量</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card warning">
          <div class="stat-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatFileSize(stats.total_size || 0) }}</div>
            <div class="stat-label">存储使用</div>
            <div class="stat-trend">
              <el-icon><Monitor /></el-icon>
              <span>已用空间</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card info">
          <div class="stat-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ daysSinceJoined }}</div>
            <div class="stat-label">使用天数</div>
            <div class="stat-trend">
              <el-icon><Calendar /></el-icon>
              <span>注册至今</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置卡片容器 -->
    <div class="settings-container">
      <!-- 基本信息卡片 -->
      <div class="settings-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon><Edit /></el-icon>
            基本信息
          </div>
          <div class="card-description">更新您的个人资料信息</div>
        </div>
        <div class="card-content">
          <el-form 
            :model="profileForm" 
            :rules="profileRules" 
            ref="profileFormRef" 
            label-position="top"
            @submit.prevent="updateProfile"
          >
            <div class="form-grid">
              <el-form-item label="用户名" prop="username">
                <el-input 
                  v-model="profileForm.username" 
                  disabled 
                  size="large"
                  prefix-icon="User"
                >
                  <template #suffix>
                    <el-icon class="disabled-icon"><Lock /></el-icon>
                  </template>
                </el-input>
                <div class="form-help">用户名创建后无法修改</div>
              </el-form-item>
              
              <el-form-item label="邮箱地址" prop="email">
                <el-input 
                  v-model="profileForm.email" 
                  size="large"
                  prefix-icon="Message"
                  placeholder="请输入邮箱地址"
                  :disabled="updating"
                />
              </el-form-item>
            </div>
            
            <div class="form-actions">
              <el-button 
                type="primary" 
                size="large" 
                @click="updateProfile" 
                :loading="updating"
                :disabled="!isProfileChanged"
              >
                <el-icon><Check /></el-icon>
                更新资料
              </el-button>
              <el-button 
                size="large" 
                @click="resetProfileForm"
                :disabled="updating"
              >
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 安全设置卡片 -->
      <div class="settings-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon><Lock /></el-icon>
            安全设置
          </div>
          <div class="card-description">修改您的登录密码以保护账户安全</div>
        </div>
        <div class="card-content">
          <el-form 
            :model="passwordForm" 
            :rules="passwordRules" 
            ref="passwordFormRef" 
            label-position="top"
            @submit.prevent="changePassword"
          >
            <div class="form-grid">
              <el-form-item label="当前密码" prop="old_password">
                <el-input 
                  v-model="passwordForm.old_password" 
                  type="password" 
                  show-password 
                  size="large"
                  prefix-icon="Lock"
                  placeholder="请输入当前密码"
                  :disabled="changingPassword"
                />
              </el-form-item>
              
              <el-form-item label="新密码" prop="new_password">
                <el-input 
                  v-model="passwordForm.new_password" 
                  type="password" 
                  show-password 
                  size="large"
                  prefix-icon="Key"
                  placeholder="请输入新密码（至少6位）"
                  :disabled="changingPassword"
                />
                <div class="password-strength" v-if="passwordForm.new_password">
                  <div class="strength-bar">
                    <div
                      class="strength-fill"
                      :class="passwordStrength.class"
                      :style="{ width: passwordStrength.width }"
                    ></div>
                  </div>
                  <div class="strength-info">
                    <span class="strength-text" :class="passwordStrength.class">
                      密码强度：{{ passwordStrength.text }}
                    </span>
                    <div v-if="passwordStrength.tips.length > 0" class="strength-tips">
                      <span class="tips-label">建议：</span>
                      <span class="tips-content">{{ passwordStrength.tips.join('、') }}</span>
                    </div>
                  </div>
                </div>
              </el-form-item>
              
              <el-form-item label="确认新密码" prop="confirm_password">
                <el-input 
                  v-model="passwordForm.confirm_password" 
                  type="password" 
                  show-password 
                  size="large"
                  prefix-icon="Key"
                  placeholder="请再次输入新密码"
                  :disabled="changingPassword"
                />
              </el-form-item>
            </div>
            
            <div class="form-actions">
              <el-button 
                type="primary" 
                size="large" 
                @click="changePassword" 
                :loading="changingPassword"
                :disabled="!isPasswordFormValid"
              >
                <el-icon><Refresh /></el-icon>
                修改密码
              </el-button>
              <el-button 
                size="large" 
                @click="resetPasswordForm"
                :disabled="changingPassword"
              >
                <el-icon><Close /></el-icon>
                清空
              </el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 管理员功能卡片 -->
      <div v-if="isAdmin" class="settings-card admin-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon><Star /></el-icon>
            管理员功能
          </div>
          <div class="card-description">管理系统用户和权限设置</div>
        </div>
        <div class="card-content">
          <div class="admin-actions">
            <el-button 
              type="primary" 
              size="large" 
              @click="showUserManagement = true"
              class="admin-btn"
            >
              <el-icon><UserFilled /></el-icon>
              用户管理
            </el-button>
            <el-button 
              type="success" 
              size="large" 
              @click="viewSystemStats"
              class="admin-btn"
            >
              <el-icon><DataAnalysis /></el-icon>
              系统统计
            </el-button>
          </div>
          
          <div class="admin-stats">
            <div class="admin-stat-item">
              <span class="stat-label">系统用户总数</span>
              <span class="stat-value">{{ allUsers.length }}</span>
            </div>
            <div class="admin-stat-item">
              <span class="stat-label">管理员数量</span>
              <span class="stat-value">{{ adminCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户管理对话框 -->
    <el-dialog
      v-model="showUserManagement"
      title="用户管理"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="user-management-content">
        <div class="management-header">
          <el-button 
            type="primary" 
            @click="loadAllUsers"
            :loading="loadingUsers"
          >
            <el-icon><Refresh /></el-icon>
            刷新列表
          </el-button>
        </div>
        
        <el-table 
          :data="allUsers" 
          v-loading="loadingUsers"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="email" label="邮箱" min-width="180" />
          <el-table-column prop="role" label="角色" width="100">
            <template #default="{ row }">
              <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
                {{ row.role === 'admin' ? '管理员' : '普通用户' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="注册时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button
                v-if="row.id !== currentUser?.id"
                size="small"
                :type="row.role === 'admin' ? 'warning' : 'primary'"
                @click="toggleUserRole(row)"
                :loading="updatingRoles.includes(row.id)"
              >
                {{ row.role === 'admin' ? '降为用户' : '升为管理员' }}
              </el-button>
              <el-button
                v-if="row.id !== currentUser?.id"
                size="small"
                type="danger"
                @click="deleteUser(row)"
                :loading="deletingUsers.includes(row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize } from '@/utils/api'
import type { Stats, UserManagement, UpdateProfileRequest, ChangePasswordRequest } from '@/types'
import {
  UserFilled,
  User,
  Edit,
  Lock,
  Check,
  Refresh,
  Close,
  Key,
  Message,
  DataAnalysis,
  Document,
  Folder,
  FolderOpened,
  DataBoard,
  Monitor,
  Clock,
  Calendar,
  TrendCharts,
  Star
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 表单引用
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 加载状态
const updating = ref(false)
const changingPassword = ref(false)
const loadingUsers = ref(false)
const updatingRoles = ref<number[]>([])
const deletingUsers = ref<number[]>([])

// 对话框状态
const showUserManagement = ref(false)

// 表单数据
const profileForm = reactive({
  username: '',
  email: ''
})

const originalProfileForm = reactive({
  username: '',
  email: ''
})

const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

// 统计数据
const stats = ref<Stats>({
  total_files: 0,
  file_count: 0,
  directory_count: 0,
  total_size: 0,
  storage_used: 0,
  total_operations: 0,
  successful_operations: 0,
  failed_operations: 0
})

// 用户管理数据
const allUsers = ref<UserManagement[]>([])

// 计算属性
const currentUser = computed(() => authStore.user)
const isAdmin = computed(() => currentUser.value?.role === 'admin')

const roleText = computed(() => isAdmin.value ? '管理员' : '普通用户')

const roleClass = computed(() => ({
  'admin-role': isAdmin.value,
  'user-role': !isAdmin.value
}))

const daysSinceJoined = computed(() => {
  if (!currentUser.value?.created_at) return 0
  const joinDate = new Date(currentUser.value.created_at)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - joinDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

const adminCount = computed(() => 
  allUsers.value.filter(user => user.role === 'admin').length
)

const isProfileChanged = computed(() => 
  profileForm.email !== originalProfileForm.email
)

const isPasswordFormValid = computed(() => 
  passwordForm.old_password && 
  passwordForm.new_password && 
  passwordForm.confirm_password &&
  passwordForm.new_password === passwordForm.confirm_password
)

// 密码强度计算
const passwordStrength = computed(() => {
  const password = passwordForm.new_password
  if (!password) return { width: '0%', class: '', text: '', tips: [] }

  let score = 0
  const tips: string[] = []

  // 长度检查
  if (password.length >= 6) {
    score++
  } else {
    tips.push('至少6个字符')
  }

  if (password.length >= 8) {
    score++
  } else if (password.length >= 6) {
    tips.push('建议8个字符以上')
  }

  // 字符类型检查
  if (/[A-Z]/.test(password)) {
    score++
  } else {
    tips.push('包含大写字母')
  }

  if (/[a-z]/.test(password)) {
    score++
  } else {
    tips.push('包含小写字母')
  }

  if (/[0-9]/.test(password)) {
    score++
  } else {
    tips.push('包含数字')
  }

  if (/[^A-Za-z0-9]/.test(password)) {
    score++
  } else {
    tips.push('包含特殊字符')
  }

  // 检查是否与旧密码相同
  if (password === passwordForm.old_password) {
    return { width: '0%', class: 'invalid', text: '不能与当前密码相同', tips: ['请使用不同的密码'] }
  }

  if (score <= 2) {
    return { width: '33%', class: 'weak', text: '弱', tips }
  }
  if (score <= 4) {
    return { width: '66%', class: 'medium', text: '中等', tips }
  }
  return { width: '100%', class: 'strong', text: '强', tips: [] }
})

// 表单验证规则
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== passwordForm.new_password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const profileRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 密码强度验证
const validatePasswordStrength = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入新密码'))
    return
  }

  if (value.length < 6) {
    callback(new Error('密码长度不能少于 6 个字符'))
    return
  }

  if (value.length > 50) {
    callback(new Error('密码长度不能超过 50 个字符'))
    return
  }

  // 检查是否与旧密码相同
  if (value === passwordForm.old_password) {
    callback(new Error('新密码不能与当前密码相同'))
    return
  }

  callback()
}

const passwordRules: FormRules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 1, max: 50, message: '密码长度应在 1-50 个字符之间', trigger: 'blur' }
  ],
  new_password: [
    { required: true, validator: validatePasswordStrength, trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 方法
const loadProfile = () => {
  if (currentUser.value) {
    profileForm.username = currentUser.value.username
    profileForm.email = currentUser.value.email
    originalProfileForm.username = currentUser.value.username
    originalProfileForm.email = currentUser.value.email
  }
}

const loadStats = async () => {
  try {
    const response = await apiMethods.system.getStats()
    if (response.data.success) {
      stats.value = response.data.data
    } else {
      throw new Error(response.data.message || '获取统计数据失败')
    }
  } catch (error: any) {
    console.error('加载统计信息失败:', error)

    // 设置默认值，避免显示异常
    stats.value = {
      total_files: 0,
      file_count: 0,
      directory_count: 0,
      total_size: 0,
      storage_used: 0,
      total_operations: 0,
      successful_operations: 0,
      failed_operations: 0
    }

    // 只在非网络错误时显示提示
    if (error.response?.status !== 401) {
      ElMessage.warning('统计数据加载失败，显示默认数据')
    }
  }
}

const loadAllUsers = async () => {
  if (!isAdmin.value) return

  try {
    loadingUsers.value = true
    const response = await apiMethods.users.list()

    if (response.data.success) {
      allUsers.value = response.data.data || []
    } else {
      throw new Error(response.data.message || '获取用户列表失败')
    }
  } catch (error: any) {
    console.error('加载用户列表失败:', error)
    allUsers.value = []

    // 根据错误类型提供不同的提示
    if (error.response?.status === 403) {
      ElMessage.warning('权限不足，无法查看用户列表')
    } else if (error.response?.status === 401) {
      // 认证错误不显示提示，让全局拦截器处理
    } else {
      ElMessage.error('加载用户列表失败')
    }
  } finally {
    loadingUsers.value = false
  }
}

const updateProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    updating.value = true

    const updateData: UpdateProfileRequest = {
      email: profileForm.email
    }

    const result = await authStore.updateProfile(updateData)

    if (result.success) {
      ElMessage.success('资料更新成功')
      originalProfileForm.email = profileForm.email

      // 刷新用户资料以确保数据同步
      await authStore.fetchProfile()
    } else {
      ElMessage.error(result.message || '更新失败')

      // 如果是邮箱已存在的错误，重置表单
      if (result.message?.includes('already exists') || result.message?.includes('已存在')) {
        resetProfileForm()
      }
    }
  } catch (error: any) {
    console.error('更新资料失败:', error)

    // 根据错误类型提供更详细的提示
    if (error.response?.status === 409) {
      ElMessage.error('邮箱已被其他用户使用')
      resetProfileForm()
    } else if (error.response?.status === 400) {
      ElMessage.error('请检查邮箱格式是否正确')
    } else {
      ElMessage.error('更新资料失败，请稍后重试')
    }
  } finally {
    updating.value = false
  }
}

const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()

    // 添加确认提示
    await ElMessageBox.confirm(
      '确定要修改密码吗？修改后需要重新登录。',
      '确认修改密码',
      {
        confirmButtonText: '确定修改',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--primary'
      }
    )

    changingPassword.value = true

    const passwordData: ChangePasswordRequest = {
      old_password: passwordForm.old_password,
      new_password: passwordForm.new_password,
      confirm_password: passwordForm.confirm_password
    }

    const result = await authStore.changePassword(passwordData)

    if (result.success) {
      ElMessage.success('密码修改成功')
      resetPasswordForm()

      // 提示用户重新登录
      setTimeout(() => {
        ElMessageBox.alert(
          '密码修改成功，为了安全起见，请重新登录。',
          '修改成功',
          {
            confirmButtonText: '重新登录',
            type: 'success'
          }
        ).then(() => {
          authStore.logout()
          window.location.href = '/login'
        })
      }, 1000)
    } else {
      ElMessage.error(result.message || '修改密码失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('修改密码失败:', error)
      ElMessage.error('修改密码失败')
    }
  } finally {
    changingPassword.value = false
  }
}

const resetProfileForm = () => {
  profileForm.email = originalProfileForm.email
}

const resetPasswordForm = () => {
  passwordForm.old_password = ''
  passwordForm.new_password = ''
  passwordForm.confirm_password = ''

  // 清除表单验证状态
  if (passwordFormRef.value) {
    passwordFormRef.value.clearValidate()
  }
}

const toggleUserRole = async (user: UserManagement) => {
  try {
    await ElMessageBox.confirm(
      `确定要将用户 "${user.username}" 的角色从 "${user.role === 'admin' ? '管理员' : '普通用户'}" 修改为 "${user.role === 'admin' ? '普通用户' : '管理员'}" 吗？`,
      '确认修改角色',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    updatingRoles.value.push(user.id)
    
    const newRole = user.role === 'admin' ? 'user' : 'admin'
    await apiMethods.users.updateRole({
      user_id: user.id,
      role: newRole
    })
    
    user.role = newRole
    ElMessage.success('用户角色修改成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改用户角色失败:', error)
      ElMessage.error('修改用户角色失败')
    }
  } finally {
    updatingRoles.value = updatingRoles.value.filter(id => id !== user.id)
  }
}

const deleteUser = async (user: UserManagement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可撤销。`,
      '确认删除用户',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    deletingUsers.value.push(user.id)
    
    await apiMethods.users.delete(user.id)
    
    allUsers.value = allUsers.value.filter(u => u.id !== user.id)
    ElMessage.success('用户删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  } finally {
    deletingUsers.value = deletingUsers.value.filter(id => id !== user.id)
  }
}

const viewSystemStats = () => {
  ElMessage.info('系统统计功能开发中')
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  loadProfile()
  loadStats()
  if (isAdmin.value) {
    loadAllUsers()
  }
})
</script>

<style scoped>
.profile-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 个人设置横幅 */
.profile-banner {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 12px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.file-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #64748b 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #64748b 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-text {
  flex: 1;
}

.banner-title {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  margin: 0 0 6px 0 !important;
  color: #1e293b !important;
  letter-spacing: -0.025em !important;
  line-height: 1.2 !important;
}

.banner-subtitle {
  font-size: 0.875rem;
  margin: 0;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.banner-visual {
  margin-left: 40px;
}

.profile-avatar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-circle {
  width: 60px;
  height: 60px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.avatar-info {
  text-align: left;
}

.avatar-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1e293b;
}

.avatar-role {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.avatar-role.admin-role {
  color: #dc2626;
}

.avatar-role.user-role {
  color: #64748b;
}

/* 统计概览 */
.stats-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: -0.025em;
}

.section-title .el-icon {
  color: #3b82f6;
  font-size: 1rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 8px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 8px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-height: 42px;
  border-left: 2px solid transparent;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
  border-left-color: #3b82f6;
}

.stat-card.success {
  border-left-color: #10b981;
}

.stat-card.warning {
  border-left-color: #f59e0b;
}

.stat-card.info {
  border-left-color: #06b6d4;
}

.stat-icon {
  width: 24px;
  height: 24px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background: #3b82f6;
}

.stat-card.success .stat-icon {
  background: #10b981;
}

.stat-card.warning .stat-icon {
  background: #f59e0b;
}

.stat-card.info .stat-icon {
  background: #06b6d4;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  line-height: 1.1;
}

.stat-label {
  color: #6b7280;
  font-size: 0.7rem;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 0.6rem;
  color: #9ca3af;
  opacity: 0.8;
}

/* 设置卡片容器 */
.settings-container {
  display: grid;
  gap: 20px;
  margin-bottom: 32px;
}

.settings-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.settings-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.admin-card {
  border-left: 3px solid #dc2626;
}

.admin-card:hover {
  border-color: #dc2626;
}

.card-header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title .el-icon {
  color: #3b82f6;
}

.admin-card .card-title .el-icon {
  color: #dc2626;
}

.card-description {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 24px;
}

.card-content {
  padding: 0 20px 20px 20px;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.form-help {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 4px;
}

.disabled-icon {
  color: #94a3b8;
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  gap: 16px;
}

/* 密码强度指示器样式 */
.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 6px;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background-color: #ef4444;
}

.strength-fill.medium {
  background-color: #f59e0b;
}

.strength-fill.strong {
  background-color: #10b981;
}

.strength-fill.invalid {
  background-color: #dc2626;
}

.strength-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.strength-text {
  font-size: 0.75rem;
  font-weight: 500;
}

.strength-text.weak {
  color: #ef4444;
}

.strength-text.medium {
  color: #f59e0b;
}

.strength-text.strong {
  color: #10b981;
}

.strength-text.invalid {
  color: #dc2626;
}

.strength-tips {
  font-size: 0.7rem;
  color: #6b7280;
  display: flex;
  gap: 4px;
}

.tips-label {
  font-weight: 500;
}

.tips-content {
  color: #9ca3af;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
}

.strength-fill.weak {
  background: #ef4444;
}

.strength-fill.medium {
  background: #f59e0b;
}

.strength-fill.strong {
  background: #10b981;
}

.strength-text {
  font-size: 0.75rem;
  font-weight: 500;
}

.strength-text.weak {
  color: #ef4444;
}

.strength-text.medium {
  color: #f59e0b;
}

.strength-text.strong {
  color: #10b981;
}

/* 管理员功能样式 */
.admin-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.admin-btn {
  flex: 1;
  min-width: 160px;
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.admin-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-stat-item .stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.admin-stat-item .stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

/* 用户管理对话框 */
.user-management-content {
  padding: 16px 0;
}

.management-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-page {
    padding: 16px;
  }
  
  .profile-banner {
    padding: 16px;
  }
  
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .banner-visual {
    margin-left: 0;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .admin-actions {
    flex-direction: column;
  }
  
  .admin-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .avatar-circle {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .el-button {
    width: 100%;
  }
}
</style>