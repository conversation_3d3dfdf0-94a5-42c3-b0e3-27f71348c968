<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传头部布局预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .upload-header {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 20px 24px;
            background: white;
            border-bottom: 1px solid #e2e8f0;
            min-height: 72px;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
        }
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            letter-spacing: -0.025em;
            white-space: nowrap;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-shrink: 0;
        }
        
        .header-stats {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: wrap;
            margin-left: 0;
            margin-top: 0;
        }
        
        .stat-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            background: #f1f5f9;
            border-radius: 16px;
            font-size: 0.8125rem;
            font-weight: 500;
            color: #64748b;
            white-space: nowrap;
            height: 28px;
        }
        
        .stat-badge.total {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }
        
        .stat-badge.size {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }
        
        .stat-badge.warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }
        
        button {
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #1e293b;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        button.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .upload-count {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 24px;
            height: 24px;
            padding: 0 8px;
            margin-left: 8px;
            background: white;
            color: #3b82f6;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 700;
        }
        
        .section-title {
            margin: 40px 0 20px 0;
            font-size: 1rem;
            color: #64748b;
            text-align: center;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .upload-header {
                padding: 16px;
                min-height: auto;
            }
            
            .header-top {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .header-title {
                font-size: 1rem;
            }
            
            .header-actions {
                width: 100%;
                justify-content: space-between;
            }
            
            .header-actions button {
                flex: 1;
            }
            
            .header-stats {
                gap: 6px;
                flex-wrap: wrap;
                margin-top: 8px;
            }
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #1e293b; margin-bottom: 40px;">上传头部布局预览</h1>
    
    <div class="section-title">桌面端视图</div>
    <div class="preview-container">
        <div class="upload-header">
            <div class="header-top">
                <h4 class="header-title">
                    📄 待上传文件
                </h4>
                <div class="header-actions">
                    <button>🗑️ 清空列表</button>
                    <button class="primary">
                        ⬆️ 开始上传
                        <span class="upload-count">5</span>
                    </button>
                </div>
            </div>
            <div class="header-stats">
                <span class="stat-badge total">
                    📁 5个文件
                </span>
                <span class="stat-badge size">
                    💾 25.6MB
                </span>
                <span class="stat-badge warning">
                    ⚠️ 2个重复
                </span>
            </div>
        </div>
    </div>
    
    <div class="section-title">移动端视图（宽度 < 768px）</div>
    <div class="preview-container" style="max-width: 375px;">
        <div class="upload-header" style="padding: 16px;">
            <div class="header-top" style="flex-direction: column; align-items: stretch; gap: 12px;">
                <h4 class="header-title" style="font-size: 1rem;">
                    📄 待上传文件
                </h4>
                <div class="header-actions" style="width: 100%; justify-content: space-between;">
                    <button style="flex: 1; margin-right: 6px;">🗑️ 清空列表</button>
                    <button class="primary" style="flex: 1; margin-left: 6px;">
                        ⬆️ 开始上传
                        <span class="upload-count">5</span>
                    </button>
                </div>
            </div>
            <div class="header-stats" style="gap: 6px; margin-top: 8px;">
                <span class="stat-badge total">
                    📁 5个文件
                </span>
                <span class="stat-badge size">
                    💾 25.6MB
                </span>
                <span class="stat-badge warning">
                    ⚠️ 2个重复
                </span>
            </div>
        </div>
    </div>
    
    <div class="section-title">上传中状态</div>
    <div class="preview-container">
        <div class="upload-header">
            <div class="header-top">
                <h4 class="header-title">
                    ⏳ 正在上传
                </h4>
            </div>
            <div class="header-stats">
                <span class="stat-badge total">
                    📁 5个文件
                </span>
                <span class="stat-badge size">
                    💾 25.6MB
                </span>
            </div>
        </div>
    </div>
</body>
</html>